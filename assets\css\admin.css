/* WP Electronic Signature Admin Styles */

/* 设置页面样式 */
.wp-esig-admin-page {
    max-width: 1200px;
}

.wp-esig-admin-page .form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
}

.wp-esig-admin-page .form-table td {
    padding: 15px 10px;
}

/* 签名记录表格样式 */
.wp-esig-signatures-table {
    margin-top: 20px;
}

.wp-esig-signatures-table .column-signature {
    width: 150px;
}

.wp-esig-signatures-table .column-status {
    width: 100px;
}

.wp-esig-signatures-table .column-actions {
    width: 120px;
}

/* 状态标签样式 */
.status-completed {
    color: #46b450;
    font-weight: bold;
}

.status-pending {
    color: #ffb900;
    font-weight: bold;
}

.status-failed {
    color: #dc3232;
    font-weight: bold;
}

/* 按钮样式 */
.wp-esig-button-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.wp-esig-button-small {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.4;
    min-height: auto;
}

/* 统计卡片样式 */
.wp-esig-stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.wp-esig-stats-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.wp-esig-stats-card h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wp-esig-stats-card .number {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    margin: 0;
}

/* 表单样式增强 */
.wp-esig-form-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 20px 0;
    padding: 20px;
}

.wp-esig-form-section h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* 帮助文本样式 */
.wp-esig-help-text {
    font-style: italic;
    color: #666;
    font-size: 13px;
    margin-top: 5px;
}

/* 合同模板管理样式 */
.wp-esig-template-editor {
    margin-top: 20px;
}

.wp-esig-template-editor .form-table th {
    width: 150px;
    vertical-align: top;
    padding-top: 20px;
}

.wp-esig-template-editor .wp-editor-container {
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* 确保编辑器切换按钮正常显示 */
.wp-esig-template-editor .wp-editor-tabs {
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
}

.wp-esig-template-editor .wp-switch-editor {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-bottom: none;
    color: #666;
    cursor: pointer;
    float: left;
    font-size: 13px;
    line-height: 1;
    margin: 0 0 0 5px;
    padding: 3px 8px 4px;
    position: relative;
    top: 1px;
}

.wp-esig-template-editor .wp-switch-editor:hover {
    background: #fff;
    color: #333;
}

.wp-esig-template-editor .wp-switch-editor.switch-tmce.active,
.wp-esig-template-editor .wp-switch-editor.switch-html.active {
    background: #fff;
    border-bottom-color: #fff;
    color: #333;
}

/* 编辑器内容区域样式 */
.wp-esig-template-editor .wp-editor-area {
    border: none;
}

.wp-esig-template-editor .wp-editor-container textarea {
    border: none;
    border-radius: 0;
    resize: vertical;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    line-height: 1.4;
}

.wp-esig-template-variables {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 15px;
    margin-top: 10px;
}

.wp-esig-template-variables h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.wp-esig-template-variables .variable-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 0;
}

.wp-esig-template-variables .variable-item {
    background: #fff;
    padding: 8px 12px;
    border-radius: 3px;
    border: 1px solid #ddd;
    font-family: monospace;
    font-size: 12px;
    color: #0073aa;
}

.wp-esig-template-preview {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 20px;
    margin-top: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.wp-esig-template-preview h4 {
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* 合同模板列表样式 */
.wp-esig-templates-table .column-name {
    width: 30%;
}

.wp-esig-templates-table .column-default {
    width: 15%;
    text-align: center;
}

.wp-esig-templates-table .column-created {
    width: 20%;
}

.wp-esig-templates-table .column-actions {
    width: 20%;
}

.wp-esig-templates-table .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* 签名区域预览样式 */
.wp-esig-signature-area-preview {
    border: 2px dashed #ddd;
    padding: 20px;
    margin: 20px 0;
    background: #fafafa;
    position: relative;
    min-height: 150px;
}

.wp-esig-signature-area-preview .party-section {
    position: absolute;
    bottom: 0;
    width: 45%;
    border: 1px solid #ddd;
    padding: 15px;
    background: #fff;
    border-radius: 5px;
}

.wp-esig-signature-area-preview .party-a {
    left: 0;
}

.wp-esig-signature-area-preview .party-b {
    right: 0;
}

.wp-esig-signature-area-preview .signature-line {
    border-bottom: 1px solid #333;
    width: 100%;
    height: 30px;
    margin: 10px 0;
    position: relative;
}

.wp-esig-signature-area-preview .signature-line::after {
    content: '（电子签名）';
    position: absolute;
    bottom: -18px;
    left: 0;
    font-size: 12px;
    color: #666;
}

/* 合同模板样式增强 */
.wp-esig-contract-template {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.8;
    color: #333;
    background: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.wp-esig-contract-template h1 {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 30px;
    border-bottom: 2px solid #333;
    padding-bottom: 10px;
}

.wp-esig-contract-template h3 {
    font-size: 16px;
    font-weight: bold;
    margin: 25px 0 15px 0;
    color: #333;
}

.wp-esig-contract-template .contract-info {
    margin-bottom: 30px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 5px;
}

.wp-esig-contract-template .signature-area {
    margin-top: 60px;
    page-break-inside: avoid;
}

/* 表格布局样式 - 确保完美对齐，避免元素覆盖 */
.wp-esig-contract-template .signature-area table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 20px;
    margin-top: 20px;
}

.wp-esig-contract-template .signature-area td {
    width: 50%;
    border: 1px solid #ddd;
    padding: 20px;
    background: #fafafa;
    border-radius: 5px;
    vertical-align: top;
}

.wp-esig-contract-template .signature-box {
    /* 保持兼容性的样式 */
    border: 1px solid #ddd;
    padding: 20px;
    background: #fafafa;
    border-radius: 5px;
}

.wp-esig-contract-template .signature-box.party-a {
    /* 甲方样式保持一致 */
}

.wp-esig-contract-template .signature-box.party-b {
    /* 乙方样式保持一致 */
}

.wp-esig-contract-template .signature-box h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    border-bottom: 1px solid #ccc;
    padding-bottom: 8px;
}

.wp-esig-contract-template .signature-line {
    border-bottom: 1px solid #333;
    height: 40px;
    margin: 10px 0;
    position: relative;
}

.wp-esig-contract-template .signature-line::after {
    content: '（电子签名）';
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 12px;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wp-esig-stats-cards {
        grid-template-columns: 1fr;
    }

    .wp-esig-button-group {
        flex-direction: column;
        align-items: stretch;
    }

    .wp-esig-admin-page .form-table th,
    .wp-esig-admin-page .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }

    .wp-esig-admin-page .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }

    .wp-esig-template-variables .variable-list {
        grid-template-columns: 1fr;
    }

    .wp-esig-signature-area-preview .party-section {
        position: static;
        width: 100%;
        margin-bottom: 20px;
    }

    .wp-esig-contract-template .signature-box {
        position: static;
        width: 100%;
        margin-bottom: 20px;
    }

    .wp-esig-contract-template .signature-area {
        min-height: auto;
    }

    .wp-esig-contract-template {
        padding: 20px;
        margin: 10px;
    }

    .wp-esig-templates-table .column-name,
    .wp-esig-templates-table .column-default,
    .wp-esig-templates-table .column-created,
    .wp-esig-templates-table .column-actions {
        width: auto;
    }
}
