<?php
/**
 * 后台管理类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 后台管理类
 */
class WP_Esig_Admin {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 添加管理菜单
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // 注册设置
        add_action('admin_init', array($this, 'register_settings'));
        
        // 加载后台资源
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // 插件设置链接
        add_filter('plugin_action_links_' . WP_ESIG_PLUGIN_BASENAME, array($this, 'add_plugin_action_links'));

        // AJAX处理
        add_action('wp_ajax_wp_esig_test_smtp', array($this, 'ajax_test_smtp'));

        // 添加调试页面（仅在调试模式下）
        if (defined('WP_DEBUG') && WP_DEBUG) {
            add_action('admin_menu', array($this, 'add_debug_menu'));
        }
    }
    
    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        // 主菜单
        add_menu_page(
            __('电子签名', 'wp-electronic-signature'),
            __('电子签名', 'wp-electronic-signature'),
            'manage_options',
            'wp-esig-settings',
            array($this, 'settings_page'),
            'dashicons-edit-page',
            56
        );
        
        // 子菜单
        add_submenu_page(
            'wp-esig-settings',
            __('设置', 'wp-electronic-signature'),
            __('设置', 'wp-electronic-signature'),
            'manage_options',
            'wp-esig-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'wp-esig-settings',
            __('合同模板', 'wp-electronic-signature'),
            __('合同模板', 'wp-electronic-signature'),
            'manage_options',
            'wp-esig-templates',
            array($this, 'templates_page')
        );

        add_submenu_page(
            'wp-esig-settings',
            __('签名记录', 'wp-electronic-signature'),
            __('签名记录', 'wp-electronic-signature'),
            'manage_options',
            'wp-esig-signatures',
            array($this, 'signatures_page')
        );

        add_submenu_page(
            'wp-esig-settings',
            __('邮件设置', 'wp-electronic-signature'),
            __('邮件设置', 'wp-electronic-signature'),
            'manage_options',
            'wp-esig-email',
            array($this, 'email_settings_page')
        );
    }
    
    /**
     * 注册设置
     */
    public function register_settings() {
        // 基本设置
        register_setting('wp_esig_settings', 'wp_esig_enabled');
        register_setting('wp_esig_settings', 'wp_esig_signature_timing');
        register_setting('wp_esig_settings', 'wp_esig_signature_page_title');
        register_setting('wp_esig_settings', 'wp_esig_signature_required');
        register_setting('wp_esig_settings', 'wp_esig_contract_template_id');

        // 甲方签名设置
        register_setting('wp_esig_settings', 'wp_esig_party_a_signature_type');
        register_setting('wp_esig_settings', 'wp_esig_party_a_signature_image');
        register_setting('wp_esig_settings', 'wp_esig_party_a_signature_text');

        // SMTP邮件设置
        register_setting('wp_esig_email_settings', 'wp_esig_smtp_enabled');
        register_setting('wp_esig_email_settings', 'wp_esig_smtp_host');
        register_setting('wp_esig_email_settings', 'wp_esig_smtp_port');
        register_setting('wp_esig_email_settings', 'wp_esig_smtp_username');
        register_setting('wp_esig_email_settings', 'wp_esig_smtp_password');
        register_setting('wp_esig_email_settings', 'wp_esig_smtp_encryption');
        register_setting('wp_esig_email_settings', 'wp_esig_smtp_from_name');
        register_setting('wp_esig_email_settings', 'wp_esig_seller_email');
        register_setting('wp_esig_email_settings', 'wp_esig_send_to_customer');
        register_setting('wp_esig_email_settings', 'wp_esig_send_to_seller');
    }
    
    /**
     * 加载后台脚本
     */
    public function enqueue_admin_scripts($hook) {
        // 只在插件页面加载
        if (strpos($hook, 'wp-esig') === false) {
            return;
        }

        // 加载WordPress编辑器相关脚本和样式
        wp_enqueue_editor();
        wp_enqueue_script('editor');
        wp_enqueue_script('quicktags');
        wp_enqueue_script('wplink');
        wp_enqueue_style('editor-buttons');

        wp_enqueue_style(
            'wp-esig-admin',
            WP_ESIG_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WP_ESIG_VERSION
        );

        wp_enqueue_script(
            'wp-esig-admin',
            WP_ESIG_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'editor', 'quicktags'),
            WP_ESIG_VERSION,
            true
        );
    }
    
    /**
     * 设置页面
     */
    public function settings_page() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        
        ?>
        <div class="wrap">
            <h1><?php _e('电子签名设置', 'wp-electronic-signature'); ?></h1>
            
            <form method="post" action="">
                <?php wp_nonce_field('wp_esig_settings', 'wp_esig_settings_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('启用电子签名', 'wp-electronic-signature'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wp_esig_enabled" value="yes" <?php checked(get_option('wp_esig_enabled', 'yes'), 'yes'); ?>>
                                <?php _e('启用电子签名功能', 'wp-electronic-signature'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('签名时机', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="hidden" name="wp_esig_signature_timing" value="on_page_load">
                            <p><strong><?php _e('页面加载时弹窗签名', 'wp-electronic-signature'); ?></strong></p>
                            <p class="description"><?php _e('客户访问结账页面时将自动弹出签名窗口', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('签名页面标题', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="text" name="wp_esig_signature_page_title" value="<?php echo esc_attr(get_option('wp_esig_signature_page_title', __('请签署合同', 'wp-electronic-signature'))); ?>" class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('签名必填', 'wp-electronic-signature'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wp_esig_signature_required" value="yes" <?php checked(get_option('wp_esig_signature_required', 'yes'), 'yes'); ?>>
                                <?php _e('签名为必填项', 'wp-electronic-signature'); ?>
                            </label>
                        </td>
                    </tr>
                </table>

                <h2><?php _e('甲方签名设置', 'wp-electronic-signature'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('甲方签名类型', 'wp-electronic-signature'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="radio" name="wp_esig_party_a_signature_type" value="text" <?php checked(get_option('wp_esig_party_a_signature_type', 'text'), 'text'); ?>>
                                    <?php _e('文字签名', 'wp-electronic-signature'); ?>
                                </label><br>
                                <label>
                                    <input type="radio" name="wp_esig_party_a_signature_type" value="image" <?php checked(get_option('wp_esig_party_a_signature_type'), 'image'); ?>>
                                    <?php _e('图片签名', 'wp-electronic-signature'); ?>
                                </label>
                            </fieldset>
                            <p class="description"><?php _e('选择甲方（卖方）的签名方式', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr class="wp-esig-text-signature" <?php echo get_option('wp_esig_party_a_signature_type', 'text') !== 'text' ? 'style="display:none;"' : ''; ?>>
                        <th scope="row"><?php _e('文字签名内容', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="text" name="wp_esig_party_a_signature_text" value="<?php echo esc_attr(get_option('wp_esig_party_a_signature_text', get_bloginfo('name'))); ?>" class="regular-text">
                            <p class="description"><?php _e('输入甲方签名文字，默认为网站名称', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr class="wp-esig-image-signature" <?php echo get_option('wp_esig_party_a_signature_type', 'text') !== 'image' ? 'style="display:none;"' : ''; ?>>
                        <th scope="row"><?php _e('签名图片', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="hidden" name="wp_esig_party_a_signature_image" id="wp_esig_party_a_signature_image" value="<?php echo esc_attr(get_option('wp_esig_party_a_signature_image')); ?>">
                            <button type="button" class="button" id="upload_signature_image"><?php _e('上传签名图片', 'wp-electronic-signature'); ?></button>
                            <button type="button" class="button" id="remove_signature_image" <?php echo !get_option('wp_esig_party_a_signature_image') ? 'style="display:none;"' : ''; ?>><?php _e('移除图片', 'wp-electronic-signature'); ?></button>
                            <div id="signature_image_preview" style="margin-top: 10px;">
                                <?php
                                $signature_image = get_option('wp_esig_party_a_signature_image');
                                if ($signature_image) {
                                    echo '<img src="' . esc_url($signature_image) . '" style="max-width: 200px; max-height: 100px; border: 1px solid #ddd; padding: 5px;">';
                                }
                                ?>
                            </div>
                            <p class="description"><?php _e('上传PNG或JPG格式的签名图片，建议尺寸：200x100像素，支持透明背景', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * 合同模板页面
     */
    public function templates_page() {
        $database = WP_Esig_Database::get_instance();

        // 处理操作
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'add':
                    $this->template_add_edit_page();
                    return;
                case 'edit':
                    // 修复：使用template_file参数而不是template_id
                    $template_file = isset($_GET['template_file']) ? sanitize_text_field($_GET['template_file']) : '';
                    $this->template_add_edit_page($template_file);
                    return;
                case 'delete':
                    $this->handle_template_file_delete();
                    break;
                case 'set_as_default':
                    $this->handle_set_template_as_default();
                    break;
                case 'save_as_default':
                    $this->handle_save_as_default_template();
                    break;
            }
        }

        // 处理表单提交
        if (isset($_POST['action'])) {
            $this->handle_template_form();
        }

        // 获取HTML模板文件列表
        $templates = $this->get_all_template_files();
        $total_templates = count($templates);

        ?>
        <div class="wrap">
            <h1>
                <?php _e('合同模板', 'wp-electronic-signature'); ?>
                <a href="<?php echo esc_url(admin_url('admin.php?page=wp-esig-templates&action=add')); ?>" class="page-title-action">
                    <?php _e('添加新模板', 'wp-electronic-signature'); ?>
                </a>

            </h1>

            <?php
            // 显示操作结果消息
            if (isset($_GET['updated'])) {
                switch ($_GET['updated']) {
                    case 'set_as_default':
                        echo '<div class="notice notice-success is-dismissible"><p>' . __('模板已成功设为默认模板文件！', 'wp-electronic-signature') . '</p></div>';
                        break;
                    case 'default_template':
                        echo '<div class="notice notice-success is-dismissible"><p>' . __('默认模板已成功同步到最新版本！', 'wp-electronic-signature') . '</p></div>';
                        break;
                }
            }
            if (isset($_GET['updated'])) {
                switch ($_GET['updated']) {
                    case 'deleted':
                        echo '<div class="notice notice-success is-dismissible"><p>' . __('模板文件已删除！', 'wp-electronic-signature') . '</p></div>';
                        break;
                }
            }
            if (isset($_GET['error'])) {
                switch ($_GET['error']) {
                    case 'save_failed':
                        echo '<div class="notice notice-error is-dismissible"><p>' . __('保存默认模板失败，请检查文件权限。', 'wp-electronic-signature') . '</p></div>';
                        break;
                    case 'template_not_found':
                        echo '<div class="notice notice-error is-dismissible"><p>' . __('未找到指定模板。', 'wp-electronic-signature') . '</p></div>';
                        break;
                    case 'file_not_found':
                        echo '<div class="notice notice-error is-dismissible"><p>' . __('模板文件不存在。', 'wp-electronic-signature') . '</p></div>';
                        break;
                    case 'delete_failed':
                        echo '<div class="notice notice-error is-dismissible"><p>' . __('删除模板文件失败，请检查文件权限。', 'wp-electronic-signature') . '</p></div>';
                        break;
                    case 'cannot_delete_default':
                        echo '<div class="notice notice-error is-dismissible"><p>' . __('不能删除默认模板文件。', 'wp-electronic-signature') . '</p></div>';
                        break;
                    case 'no_template_id':
                        echo '<div class="notice notice-error is-dismissible"><p>' . __('缺少模板ID参数。', 'wp-electronic-signature') . '</p></div>';
                        break;
                    case 'update_failed':
                        echo '<div class="notice notice-error is-dismissible"><p>' . __('模板同步失败，请重试。', 'wp-electronic-signature') . '</p></div>';
                        break;
                }
            }
            ?>

            <table class="wp-list-table widefat fixed striped wp-esig-templates-table">
                <thead>
                    <tr>
                        <th><?php _e('文件名', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('模板名称', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('默认模板', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('文件大小', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('修改时间', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('操作', 'wp-electronic-signature'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($templates): ?>
                        <?php foreach ($templates as $template): ?>
                            <tr>
                                <td><code><?php echo esc_html($template->filename); ?></code></td>
                                <td>
                                    <strong>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=wp-esig-templates&action=edit&template_file=' . urlencode($template->filename))); ?>">
                                            <?php echo esc_html($template->name); ?>
                                        </a>
                                    </strong>
                                </td>
                                <td>
                                    <?php if ($template->is_default): ?>
                                        <span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span>
                                        <span style="color: #46b450;"><?php _e('是', 'wp-electronic-signature'); ?></span>
                                    <?php else: ?>
                                        <span class="dashicons dashicons-minus" style="color: #ddd;"></span>
                                        <span style="color: #666;"><?php _e('否', 'wp-electronic-signature'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo size_format($template->file_size); ?></td>
                                <td><?php echo date('Y-m-d H:i:s', $template->modified_time); ?></td>
                                <td>
                                    <a href="<?php echo esc_url(admin_url('admin.php?page=wp-esig-templates&action=edit&template_file=' . urlencode($template->filename))); ?>" class="button button-small">
                                        <?php _e('编辑', 'wp-electronic-signature'); ?>
                                    </a>
                                    <?php if (!$template->is_default): ?>
                                        <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin.php?page=wp-esig-templates&action=set_as_default&template_file=' . urlencode($template->filename)), 'set_as_default_template')); ?>"
                                           class="button button-small button-primary"
                                           onclick="return confirm('<?php _e('确定要将当前模板设为默认模板文件吗？这将覆盖 templates/default-contract.html 文件。', 'wp-electronic-signature'); ?>')">
                                            <?php _e('设为默认', 'wp-electronic-signature'); ?>
                                        </a>
                                        <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin.php?page=wp-esig-templates&action=delete&template_file=' . urlencode($template->filename)), 'delete_template_' . $template->filename)); ?>"
                                           class="button button-small"
                                           onclick="return confirm('<?php _e('确定要删除这个模板文件吗？', 'wp-electronic-signature'); ?>')">
                                            <?php _e('删除', 'wp-electronic-signature'); ?>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6"><?php _e('暂无合同模板文件', 'wp-electronic-signature'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <p class="description">
                <?php printf(__('共找到 %d 个模板文件。模板文件保存在 %s 目录中。', 'wp-electronic-signature'), $total_templates, '<code>templates/</code>'); ?>
            </p>
        </div>
        <?php
    }

    /**
     * 签名记录页面
     */
    public function signatures_page() {
        $database = WP_Esig_Database::get_instance();
        
        // 分页参数
        $per_page = 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;
        
        // 获取签名记录
        $signatures = $database->get_signatures($per_page, $offset);
        $total_signatures = $database->get_signatures_count();
        $total_pages = ceil($total_signatures / $per_page);
        
        ?>
        <div class="wrap">
            <h1><?php _e('签名记录', 'wp-electronic-signature'); ?></h1>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('ID', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('订单号', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('客户', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('签名时间', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('状态', 'wp-electronic-signature'); ?></th>
                        <th><?php _e('操作', 'wp-electronic-signature'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($signatures): ?>
                        <?php foreach ($signatures as $signature): ?>
                            <?php
                            $order = wc_get_order($signature->order_id);
                            $user = get_user_by('id', $signature->user_id);
                            ?>
                            <tr>
                                <td><?php echo esc_html($signature->id); ?></td>
                                <td>
                                    <?php if ($order): ?>
                                        <a href="<?php echo esc_url(admin_url('post.php?post=' . $signature->order_id . '&action=edit')); ?>">
                                            <?php echo esc_html($order->get_order_number()); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo esc_html($signature->order_id); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user): ?>
                                        <?php echo esc_html($user->display_name); ?>
                                    <?php else: ?>
                                        <?php _e('访客', 'wp-electronic-signature'); ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($signature->signed_at); ?></td>
                                <td>
                                    <span class="status-<?php echo esc_attr($signature->status); ?>">
                                        <?php echo esc_html(ucfirst($signature->status)); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="<?php echo esc_url($this->get_signature_image_url($signature->signature_image)); ?>" target="_blank" class="button button-small">
                                        <?php _e('查看签名', 'wp-electronic-signature'); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6"><?php _e('暂无签名记录', 'wp-electronic-signature'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <?php if ($total_pages > 1): ?>
                <div class="tablenav">
                    <div class="tablenav-pages">
                        <?php
                        echo paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => $total_pages,
                            'current' => $current_page
                        ));
                        ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 模板添加/编辑页面
     */
    public function template_add_edit_page($template_file = null) {
        $template = null;
        $is_edit = false;

        if ($template_file) {
            $template = $this->get_template_by_filename($template_file);
            $is_edit = true;

            if (!$template) {
                wp_die(__('模板文件不存在', 'wp-electronic-signature'));
            }
        }

        ?>
        <div class="wrap wp-esig-admin-page">
            <h1>
                <?php echo $is_edit ? __('编辑合同模板', 'wp-electronic-signature') : __('添加合同模板', 'wp-electronic-signature'); ?>
                <a href="<?php echo esc_url(admin_url('admin.php?page=wp-esig-templates')); ?>" class="page-title-action">
                    <?php _e('返回列表', 'wp-electronic-signature'); ?>
                </a>
            </h1>

            <form method="post" action="<?php echo esc_url(admin_url('admin.php?page=wp-esig-templates')); ?>" class="wp-esig-template-editor">
                <?php wp_nonce_field('wp_esig_template_save', 'wp_esig_template_nonce'); ?>
                <input type="hidden" name="action" value="<?php echo $is_edit ? 'update' : 'save'; ?>">
                <?php if ($is_edit): ?>
                    <input type="hidden" name="template_file" value="<?php echo esc_attr($template->filename); ?>">
                <?php endif; ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="template_name"><?php _e('模板名称', 'wp-electronic-signature'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="template_name" name="template_name"
                                   value="<?php echo $template ? esc_attr($template->name) : ''; ?>"
                                   class="regular-text" required>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="is_default"><?php _e('设为默认模板', 'wp-electronic-signature'); ?></label>
                        </th>
                        <td>
                            <label>
                                <input type="checkbox" id="is_default" name="is_default" value="1"
                                       <?php checked($template ? $template->is_default : 0, 1); ?>>
                                <?php _e('设为默认合同模板', 'wp-electronic-signature'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="template_content"><?php _e('模板内容', 'wp-electronic-signature'); ?></label>
                        </th>
                        <td>
                            <?php
                            $content = $template ? $template->content : $this->get_default_contract_template_with_signature_area();
                            wp_editor($content, 'template_content', array(
                                'textarea_name' => 'template_content',
                                'textarea_rows' => 20,
                                'media_buttons' => false,
                                'teeny' => false,
                                'quicktags' => array(
                                    'buttons' => 'strong,em,link,block,del,ins,img,ul,ol,li,code,more,close'
                                ),
                                'tinymce' => array(
                                    'toolbar1' => 'bold,italic,underline,strikethrough,|,bullist,numlist,|,link,unlink,|,undo,redo',
                                    'toolbar2' => 'formatselect,|,pastetext,removeformat,|,charmap,|,outdent,indent,|,wp_more',
                                    'resize' => true,
                                    'wordpress_adv_hidden' => false,
                                    'add_unload_trigger' => false
                                ),
                                'wpautop' => true,
                                'editor_class' => 'wp-esig-template-editor-content'
                            ));
                            ?>
                            <div class="wp-esig-template-variables">
                                <h4><?php _e('可用变量', 'wp-electronic-signature'); ?></h4>
                                <div class="variable-list">
                                    <div class="variable-item">{order_number} - <?php _e('订单号', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{customer_name} - <?php _e('客户姓名', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{customer_email} - <?php _e('客户邮箱', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{order_total} - <?php _e('订单总额', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{order_date} - <?php _e('订单日期', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{order_items} - <?php _e('订单商品', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{signature_date} - <?php _e('签署日期', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{party_a_name} - <?php _e('甲方姓名', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{party_a_date} - <?php _e('甲方签署时间', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{party_b_name} - <?php _e('乙方姓名', 'wp-electronic-signature'); ?></div>
                                    <div class="variable-item">{party_b_date} - <?php _e('乙方签署时间', 'wp-electronic-signature'); ?></div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <?php submit_button($is_edit ? __('更新模板', 'wp-electronic-signature') : __('保存模板', 'wp-electronic-signature'), 'primary', 'submit', false); ?>


                </p>
            </form>
        </div>
        <?php
    }

    /**
     * 处理模板表单提交
     */
    public function handle_template_form() {
        if (!wp_verify_nonce($_POST['wp_esig_template_nonce'], 'wp_esig_template_save')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        $template_name = sanitize_text_field($_POST['template_name']);
        $template_content = wp_kses_post($_POST['template_content']);
        $is_default = isset($_POST['is_default']) ? 1 : 0;

        if ($_POST['action'] === 'save') {
            // 添加新模板 - 直接保存为HTML文件
            $result = $this->save_template_as_html_file($template_name, $template_content, $is_default);
            if ($result) {
                echo '<div class="notice notice-success"><p>' . __('模板已保存为HTML文件', 'wp-electronic-signature') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('保存失败', 'wp-electronic-signature') . '</p></div>';
            }
        } elseif ($_POST['action'] === 'update') {
            // 更新模板 - 更新对应的HTML文件
            $template_file = sanitize_text_field($_POST['template_file']);
            $result = $this->update_template_html_file_by_filename($template_file, $template_name, $template_content, $is_default);
            if ($result) {
                echo '<div class="notice notice-success"><p>' . __('模板文件已更新', 'wp-electronic-signature') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('更新失败', 'wp-electronic-signature') . '</p></div>';
            }
        }
    }

    /**
     * 处理模板删除
     */
    public function handle_template_delete() {
        $template_id = intval($_GET['template_id']);

        if (!wp_verify_nonce($_GET['_wpnonce'], 'delete_template_' . $template_id)) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        $database = WP_Esig_Database::get_instance();
        $result = $database->delete_contract_template($template_id);

        if ($result) {
            echo '<div class="notice notice-success"><p>' . __('模板已删除', 'wp-electronic-signature') . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>' . __('删除失败', 'wp-electronic-signature') . '</p></div>';
        }
    }

    /**
     * 获取带签名区域的默认合同模板
     */
    private function get_default_contract_template_with_signature_area() {
        // 使用与主插件相同的巴西合同模板
        $main_plugin = WP_Electronic_Signature::get_instance();
        return $main_plugin->get_default_contract_template();
    }

    /**
     * 保存设置
     */
    private function save_settings() {
        if (!wp_verify_nonce($_POST['wp_esig_settings_nonce'], 'wp_esig_settings')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }
        
        update_option('wp_esig_enabled', isset($_POST['wp_esig_enabled']) ? 'yes' : 'no');
        update_option('wp_esig_signature_timing', sanitize_text_field($_POST['wp_esig_signature_timing']));
        update_option('wp_esig_signature_page_title', sanitize_text_field($_POST['wp_esig_signature_page_title']));
        update_option('wp_esig_signature_required', isset($_POST['wp_esig_signature_required']) ? 'yes' : 'no');
        
        echo '<div class="notice notice-success"><p>' . __('设置已保存', 'wp-electronic-signature') . '</p></div>';
    }
    
    /**
     * 获取签名图片URL
     */
    private function get_signature_image_url($filename) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['baseurl'] . '/wp-esig-signatures/' . $filename;
    }
    
    /**
     * 保存模板为HTML文件
     */
    public function save_template_as_html_file($template_name, $template_content, $is_default = 0) {
        // 确保templates目录存在
        $templates_dir = WP_ESIG_PLUGIN_DIR . 'templates/';
        if (!file_exists($templates_dir)) {
            wp_mkdir_p($templates_dir);
        }

        // 生成文件名（使用模板名称，去除特殊字符）
        $filename = $this->sanitize_filename($template_name) . '.html';
        $file_path = $templates_dir . $filename;

        // 如果设为默认模板，保存为default-contract.html
        if ($is_default) {
            $default_file_path = $templates_dir . 'default-contract.html';
            $default_result = file_put_contents($default_file_path, $template_content);
            if ($default_result === false) {
                error_log('WP Electronic Signature: 保存默认模板文件失败: ' . $default_file_path);
                return false;
            }
        }

        // 保存模板文件
        $result = file_put_contents($file_path, $template_content);

        if ($result !== false) {
            error_log('WP Electronic Signature: 模板已保存为HTML文件: ' . $file_path);
            return true;
        } else {
            error_log('WP Electronic Signature: 保存模板文件失败: ' . $file_path);
            return false;
        }
    }

    /**
     * 更新模板HTML文件
     */
    public function update_template_html_file($template_id, $template_name, $template_content, $is_default = 0) {
        // 这里需要根据template_id找到对应的文件名
        // 由于我们改为文件存储，需要一个映射机制
        $templates_dir = WP_ESIG_PLUGIN_DIR . 'templates/';
        $filename = $this->sanitize_filename($template_name) . '.html';
        $file_path = $templates_dir . $filename;

        // 如果设为默认模板，同时更新default-contract.html
        if ($is_default) {
            $default_file_path = $templates_dir . 'default-contract.html';
            $default_result = file_put_contents($default_file_path, $template_content);
            if ($default_result === false) {
                error_log('WP Electronic Signature: 更新默认模板文件失败: ' . $default_file_path);
                return false;
            }
        }

        // 更新模板文件
        $result = file_put_contents($file_path, $template_content);

        if ($result !== false) {
            error_log('WP Electronic Signature: 模板文件已更新: ' . $file_path);
            return true;
        } else {
            error_log('WP Electronic Signature: 更新模板文件失败: ' . $file_path);
            return false;
        }
    }

    /**
     * 清理文件名，移除特殊字符
     */
    private function sanitize_filename($filename) {
        // 移除特殊字符，只保留字母、数字、中文、连字符和下划线
        $filename = preg_replace('/[^\w\-\x{4e00}-\x{9fa5}]/u', '_', $filename);
        // 移除多个连续的下划线
        $filename = preg_replace('/_+/', '_', $filename);
        // 移除开头和结尾的下划线
        $filename = trim($filename, '_');
        // 如果文件名为空，使用默认名称
        if (empty($filename)) {
            $filename = 'template_' . time();
        }
        return $filename;
    }

    /**
     * 获取所有模板HTML文件
     */
    public function get_all_template_files() {
        $templates_dir = WP_ESIG_PLUGIN_DIR . 'templates/';
        $templates = array();

        if (!is_dir($templates_dir)) {
            return $templates;
        }

        $files = scandir($templates_dir);
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'html') {
                $file_path = $templates_dir . $file;
                $file_content = file_get_contents($file_path);

                $templates[] = (object) array(
                    'filename' => $file,
                    'name' => $this->get_template_name_from_filename($file),
                    'content' => $file_content,
                    'is_default' => ($file === 'default-contract.html'),
                    'file_size' => filesize($file_path),
                    'modified_time' => filemtime($file_path),
                    'file_path' => $file_path
                );
            }
        }

        // 按修改时间排序，最新的在前
        usort($templates, function($a, $b) {
            return $b->modified_time - $a->modified_time;
        });

        return $templates;
    }

    /**
     * 从文件名获取模板名称
     */
    private function get_template_name_from_filename($filename) {
        $name = pathinfo($filename, PATHINFO_FILENAME);

        // 如果是默认模板，返回特殊名称
        if ($filename === 'default-contract.html') {
            return __('默认合同模板', 'wp-electronic-signature');
        }

        // 将下划线替换为空格，首字母大写
        $name = str_replace('_', ' ', $name);
        return ucfirst($name);
    }

    /**
     * 根据文件名获取模板信息
     */
    public function get_template_by_filename($filename) {
        $templates_dir = WP_ESIG_PLUGIN_DIR . 'templates/';
        $file_path = $templates_dir . $filename;

        if (!file_exists($file_path)) {
            return null;
        }

        $file_content = file_get_contents($file_path);

        return (object) array(
            'filename' => $filename,
            'name' => $this->get_template_name_from_filename($filename),
            'content' => $file_content,
            'is_default' => ($filename === 'default-contract.html'),
            'file_size' => filesize($file_path),
            'modified_time' => filemtime($file_path),
            'file_path' => $file_path
        );
    }

    /**
     * 根据文件名更新模板HTML文件
     */
    public function update_template_html_file_by_filename($old_filename, $template_name, $template_content, $is_default = 0) {
        $templates_dir = WP_ESIG_PLUGIN_DIR . 'templates/';
        $old_file_path = $templates_dir . $old_filename;

        // 生成新文件名
        $new_filename = $this->sanitize_filename($template_name) . '.html';
        $new_file_path = $templates_dir . $new_filename;

        // 如果文件名改变了，删除旧文件
        if ($old_filename !== $new_filename && file_exists($old_file_path)) {
            unlink($old_file_path);
        }

        // 如果设为默认模板，同时更新default-contract.html
        if ($is_default) {
            $default_file_path = $templates_dir . 'default-contract.html';
            $default_result = file_put_contents($default_file_path, $template_content);
            if ($default_result === false) {
                error_log('WP Electronic Signature: 更新默认模板文件失败: ' . $default_file_path);
                return false;
            }
        }

        // 保存新文件
        $result = file_put_contents($new_file_path, $template_content);

        if ($result !== false) {
            error_log('WP Electronic Signature: 模板文件已更新: ' . $new_file_path);
            return true;
        } else {
            error_log('WP Electronic Signature: 更新模板文件失败: ' . $new_file_path);
            return false;
        }
    }

    /**
     * 处理HTML模板文件删除
     */
    public function handle_template_file_delete() {
        $template_file = isset($_GET['template_file']) ? sanitize_text_field($_GET['template_file']) : '';

        if (!$template_file) {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'no_template_file'
            ), admin_url('admin.php')));
            exit;
        }

        if (!wp_verify_nonce($_GET['_wpnonce'], 'delete_template_' . $template_file)) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        // 不允许删除默认模板
        if ($template_file === 'default-contract.html') {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'cannot_delete_default'
            ), admin_url('admin.php')));
            exit;
        }

        $templates_dir = WP_ESIG_PLUGIN_DIR . 'templates/';
        $file_path = $templates_dir . $template_file;

        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                wp_redirect(add_query_arg(array(
                    'page' => 'wp-esig-templates',
                    'updated' => 'deleted'
                ), admin_url('admin.php')));
                exit;
            } else {
                wp_redirect(add_query_arg(array(
                    'page' => 'wp-esig-templates',
                    'error' => 'delete_failed'
                ), admin_url('admin.php')));
                exit;
            }
        } else {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'file_not_found'
            ), admin_url('admin.php')));
            exit;
        }
    }

    /**
     * 处理设置HTML模板为默认模板
     */
    public function handle_set_template_as_default() {
        $template_file = isset($_GET['template_file']) ? sanitize_text_field($_GET['template_file']) : '';

        if (!$template_file) {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'no_template_file'
            ), admin_url('admin.php')));
            exit;
        }

        if (!wp_verify_nonce($_GET['_wpnonce'], 'set_as_default_template')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        $templates_dir = WP_ESIG_PLUGIN_DIR . 'templates/';
        $source_file = $templates_dir . $template_file;
        $default_file = $templates_dir . 'default-contract.html';

        if (!file_exists($source_file)) {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'file_not_found'
            ), admin_url('admin.php')));
            exit;
        }

        // 复制文件内容到默认模板
        $content = file_get_contents($source_file);
        $result = file_put_contents($default_file, $content);

        if ($result !== false) {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'updated' => 'set_as_default'
            ), admin_url('admin.php')));
            exit;
        } else {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'save_failed'
            ), admin_url('admin.php')));
            exit;
        }
    }

    /**
     * 处理设为默认模板（保留原有功能，用于数据库中的模板）
     */
    public function handle_save_as_default_template() {
        if (!wp_verify_nonce($_GET['_wpnonce'], 'save_as_default_template')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        $template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;

        if (!$template_id) {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'no_template_id'
            ), admin_url('admin.php')));
            exit;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'esig_contract_templates';

        // 获取指定模板的内容
        $template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND status = 'active'",
            $template_id
        ));

        if (!$template) {
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'template_not_found'
            ), admin_url('admin.php')));
            exit;
        }

        // 将模板内容保存到HTML文件
        $template_file = WP_ESIG_PLUGIN_DIR . 'templates/default-contract.html';
        $result = file_put_contents($template_file, $template->content);

        if ($result !== false) {
            // 重定向到模板列表页面并显示成功消息
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'updated' => 'set_as_default'
            ), admin_url('admin.php')));
            exit;
        } else {
            // 重定向到模板列表页面并显示错误消息
            wp_redirect(add_query_arg(array(
                'page' => 'wp-esig-templates',
                'error' => 'save_failed'
            ), admin_url('admin.php')));
            exit;
        }
    }

    /**
     * 添加插件操作链接
     */
    public function add_plugin_action_links($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=wp-esig-settings') . '">' . __('设置', 'wp-electronic-signature') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * 添加调试菜单
     */
    public function add_debug_menu() {
        add_submenu_page(
            'wp-esig-settings',
            __('调试信息', 'wp-electronic-signature'),
            __('调试信息', 'wp-electronic-signature'),
            'manage_options',
            'wp-esig-debug',
            array($this, 'debug_page')
        );
    }

    /**
     * 调试页面
     */
    public function debug_page() {
        global $wpdb;

        echo '<div class="wrap">';
        echo '<h1>' . __('WP Electronic Signature - 调试信息', 'wp-electronic-signature') . '</h1>';

        // 检查数据库表
        echo '<h2>数据库表状态</h2>';
        $templates_table = $wpdb->prefix . 'esig_contract_templates';
        $signatures_table = $wpdb->prefix . 'esig_signatures';

        $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") == $templates_table;
        $signatures_exists = $wpdb->get_var("SHOW TABLES LIKE '$signatures_table'") == $signatures_table;

        echo '<p>合同模板表 (' . $templates_table . '): ' . ($templates_exists ? '<span style="color: green;">✓ 存在</span>' : '<span style="color: red;">✗ 不存在</span>') . '</p>';
        echo '<p>签名记录表 (' . $signatures_table . '): ' . ($signatures_exists ? '<span style="color: green;">✓ 存在</span>' : '<span style="color: red;">✗ 不存在</span>') . '</p>';

        // 检查合同模板
        if ($templates_exists) {
            echo '<h2>合同模板</h2>';
            $templates = $wpdb->get_results("SELECT * FROM $templates_table WHERE status = 'active'");

            if ($templates) {
                echo '<table class="wp-list-table widefat fixed striped">';
                echo '<thead><tr><th>ID</th><th>名称</th><th>默认</th><th>内容长度</th><th>创建时间</th></tr></thead>';
                echo '<tbody>';
                foreach ($templates as $template) {
                    echo '<tr>';
                    echo '<td>' . $template->id . '</td>';
                    echo '<td>' . esc_html($template->name) . '</td>';
                    echo '<td>' . ($template->is_default ? '是' : '否') . '</td>';
                    echo '<td>' . strlen($template->content) . ' 字符</td>';
                    echo '<td>' . $template->created_at . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            } else {
                echo '<p style="color: red;">没有找到活跃的合同模板</p>';
            }
        }

        // 检查插件设置
        echo '<h2>插件设置</h2>';
        $settings = array(
            'wp_esig_enabled' => get_option('wp_esig_enabled'),
            'wp_esig_signature_timing' => get_option('wp_esig_signature_timing'),
            'wp_esig_signature_required' => get_option('wp_esig_signature_required'),
            'wp_esig_contract_template_id' => get_option('wp_esig_contract_template_id')
        );

        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>设置项</th><th>值</th></tr></thead>';
        echo '<tbody>';
        foreach ($settings as $key => $value) {
            echo '<tr>';
            echo '<td>' . $key . '</td>';
            echo '<td>' . ($value ? esc_html($value) : '<em>未设置</em>') . '</td>';
            echo '</tr>';
        }
        echo '</tbody></table>';

        // 修复按钮
        echo '<h2>修复工具</h2>';
        echo '<p><button id="fix-database" class="button button-primary">重新创建数据库表和默认模板</button></p>';
        echo '<div id="fix-result" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border: 1px solid #ddd; display: none;"></div>';

        // 测试AJAX端点
        echo '<h2>AJAX测试</h2>';
        echo '<button id="test-ajax" class="button">测试合同内容加载</button>';
        echo '<div id="ajax-result" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border: 1px solid #ddd; display: none;"></div>';

        echo '<script>
        jQuery(document).ready(function($) {
            $("#fix-database").click(function() {
                if (!confirm("确定要重新创建数据库表和默认模板吗？这将删除现有数据！")) {
                    return;
                }

                $("#fix-result").show().html("正在修复...");

                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "wp_esig_fix_database",
                        nonce: "' . wp_create_nonce('wp_esig_fix_database') . '"
                    },
                    success: function(response) {
                        $("#fix-result").html("<h4>修复结果:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>");
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    },
                    error: function(xhr, status, error) {
                        $("#fix-result").html("<h4>修复失败:</h4><p>状态: " + status + "</p><p>错误: " + error + "</p><pre>" + xhr.responseText + "</pre>");
                    }
                });
            });

            $("#test-ajax").click(function() {
                $("#ajax-result").show().html("正在测试...");

                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "get_contract_content",
                        nonce: "' . wp_create_nonce('wp_esig_signature_nonce') . '",
                        customer_name: "测试用户",
                        customer_email: "<EMAIL>",
                        customer_phone: "123456789"
                    },
                    success: function(response) {
                        $("#ajax-result").html("<h4>成功响应:</h4><pre>" + JSON.stringify(response, null, 2) + "</pre>");
                    },
                    error: function(xhr, status, error) {
                        $("#ajax-result").html("<h4>错误响应:</h4><p>状态: " + status + "</p><p>错误: " + error + "</p><pre>" + xhr.responseText + "</pre>");
                    }
                });
            });
        });
        </script>';

        echo '</div>';
    }

    /**
     * 邮件设置页面
     */
    public function email_settings_page() {
        if (isset($_POST['submit'])) {
            $this->save_email_settings();
        }

        if (isset($_POST['test_smtp'])) {
            $this->test_smtp_connection();
        }

        ?>
        <div class="wrap">
            <h1><?php _e('邮件设置', 'wp-electronic-signature'); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('wp_esig_email_settings', 'wp_esig_email_settings_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('启用自定义SMTP', 'wp-electronic-signature'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wp_esig_smtp_enabled" value="yes" <?php checked(get_option('wp_esig_smtp_enabled', 'no'), 'yes'); ?>>
                                <?php _e('使用自定义SMTP服务器发送邮件', 'wp-electronic-signature'); ?>
                            </label>
                            <p class="description"><?php _e('启用后将使用下面配置的SMTP服务器发送合同邮件', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('SMTP服务器', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="text" name="wp_esig_smtp_host" value="<?php echo esc_attr(get_option('wp_esig_smtp_host', '')); ?>" class="regular-text">
                            <p class="description"><?php _e('例如：smtp.gmail.com 或 smtp.qq.com', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('SMTP端口', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="number" name="wp_esig_smtp_port" value="<?php echo esc_attr(get_option('wp_esig_smtp_port', '587')); ?>" class="small-text">
                            <p class="description"><?php _e('常用端口：587 (TLS) 或 465 (SSL)', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('加密方式', 'wp-electronic-signature'); ?></th>
                        <td>
                            <select name="wp_esig_smtp_encryption">
                                <option value="tls" <?php selected(get_option('wp_esig_smtp_encryption', 'tls'), 'tls'); ?>>TLS</option>
                                <option value="ssl" <?php selected(get_option('wp_esig_smtp_encryption'), 'ssl'); ?>>SSL</option>
                                <option value="" <?php selected(get_option('wp_esig_smtp_encryption'), ''); ?>><?php _e('无加密', 'wp-electronic-signature'); ?></option>
                            </select>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('用户名', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="text" name="wp_esig_smtp_username" value="<?php echo esc_attr(get_option('wp_esig_smtp_username', '')); ?>" class="regular-text">
                            <p class="description"><?php _e('通常是您的邮箱地址', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('密码', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="password" name="wp_esig_smtp_password" value="<?php echo esc_attr(get_option('wp_esig_smtp_password', '')); ?>" class="regular-text">
                            <p class="description"><?php _e('邮箱密码或应用专用密码', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('发件人名称', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="text" name="wp_esig_smtp_from_name" value="<?php echo esc_attr(get_option('wp_esig_smtp_from_name', get_bloginfo('name'))); ?>" class="regular-text">
                            <p class="description"><?php _e('邮件中显示的发件人名称', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>
                </table>

                <h2><?php _e('邮件发送设置', 'wp-electronic-signature'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('卖家邮箱地址', 'wp-electronic-signature'); ?></th>
                        <td>
                            <input type="email" name="wp_esig_seller_email" value="<?php echo esc_attr(get_option('wp_esig_seller_email', get_option('admin_email'))); ?>" class="regular-text">
                            <p class="description"><?php _e('卖家（甲方）接收合同PDF的邮箱地址，默认为管理员邮箱', 'wp-electronic-signature'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('发送给客户', 'wp-electronic-signature'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wp_esig_send_to_customer" value="yes" <?php checked(get_option('wp_esig_send_to_customer', 'yes'), 'yes'); ?>>
                                <?php _e('签名完成后发送PDF合同给客户', 'wp-electronic-signature'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('发送给卖家', 'wp-electronic-signature'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wp_esig_send_to_seller" value="yes" <?php checked(get_option('wp_esig_send_to_seller', 'yes'), 'yes'); ?>>
                                <?php _e('签名完成后发送PDF合同给卖家', 'wp-electronic-signature'); ?>
                            </label>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <?php submit_button(__('保存设置', 'wp-electronic-signature'), 'primary', 'submit', false); ?>
                    <input type="submit" name="test_smtp" class="button button-secondary" value="<?php _e('测试SMTP连接', 'wp-electronic-signature'); ?>" style="margin-left: 10px;">
                </p>
            </form>

            <div class="wp-esig-email-help">
                <h3><?php _e('常用SMTP配置示例', 'wp-electronic-signature'); ?></h3>
                <div style="background: #f9f9f9; padding: 15px; border: 1px solid #ddd;">
                    <h4>Gmail:</h4>
                    <p>服务器: smtp.gmail.com | 端口: 587 | 加密: TLS</p>

                    <h4>QQ邮箱:</h4>
                    <p>服务器: smtp.qq.com | 端口: 587 | 加密: TLS</p>

                    <h4>163邮箱:</h4>
                    <p>服务器: smtp.163.com | 端口: 587 | 加密: TLS</p>

                    <h4>阿里云邮箱:</h4>
                    <p>服务器: smtp.mxhichina.com | 端口: 587 | 加密: TLS</p>
                </div>
            </div>

            <script>
            jQuery(document).ready(function($) {
                // 异步测试SMTP连接
                $('#test_smtp_async').on('click', function(e) {
                    e.preventDefault();

                    var $button = $(this);
                    var $result = $('#smtp-test-result');

                    // 显示加载状态
                    $button.prop('disabled', true).val('测试中...');
                    $result.html('<div class="notice notice-info"><p>正在测试SMTP连接...</p></div>');

                    // 发送AJAX请求
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'wp_esig_test_smtp',
                            nonce: '<?php echo wp_create_nonce('wp_esig_test_smtp'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $result.html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                            } else {
                                $result.html('<div class="notice notice-error"><p>' + response.data + '</p></div>');
                            }
                        },
                        error: function() {
                            $result.html('<div class="notice notice-error"><p>测试请求失败，请重试。</p></div>');
                        },
                        complete: function() {
                            $button.prop('disabled', false).val('异步测试SMTP连接');
                        }
                    });
                });
            });
            </script>

            <div id="smtp-test-result" style="margin-top: 20px;"></div>

            <p class="submit">
                <input type="button" id="test_smtp_async" class="button button-secondary" value="异步测试SMTP连接" style="margin-top: 10px;">
            </p>
        </div>
        <?php
    }

    /**
     * 保存邮件设置
     */
    private function save_email_settings() {
        if (!wp_verify_nonce($_POST['wp_esig_email_settings_nonce'], 'wp_esig_email_settings')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        update_option('wp_esig_smtp_enabled', isset($_POST['wp_esig_smtp_enabled']) ? 'yes' : 'no');
        update_option('wp_esig_smtp_host', sanitize_text_field($_POST['wp_esig_smtp_host']));
        update_option('wp_esig_smtp_port', intval($_POST['wp_esig_smtp_port']));
        update_option('wp_esig_smtp_username', sanitize_text_field($_POST['wp_esig_smtp_username']));
        update_option('wp_esig_smtp_password', sanitize_text_field($_POST['wp_esig_smtp_password']));
        update_option('wp_esig_smtp_encryption', sanitize_text_field($_POST['wp_esig_smtp_encryption']));
        update_option('wp_esig_smtp_from_name', sanitize_text_field($_POST['wp_esig_smtp_from_name']));
        update_option('wp_esig_seller_email', sanitize_email($_POST['wp_esig_seller_email']));
        update_option('wp_esig_send_to_customer', isset($_POST['wp_esig_send_to_customer']) ? 'yes' : 'no');
        update_option('wp_esig_send_to_seller', isset($_POST['wp_esig_send_to_seller']) ? 'yes' : 'no');

        echo '<div class="notice notice-success"><p>' . __('邮件设置已保存', 'wp-electronic-signature') . '</p></div>';
    }

    /**
     * 测试SMTP连接
     */
    private function test_smtp_connection() {
        if (!wp_verify_nonce($_POST['wp_esig_email_settings_nonce'], 'wp_esig_email_settings')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        // 先保存设置
        $this->save_email_settings();

        // 测试连接
        $email_class = WP_Esig_Email::get_instance();
        $result = $email_class->test_smtp_connection();

        if ($result['success']) {
            echo '<div class="notice notice-success"><p>' . esc_html($result['message']) . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>' . esc_html($result['message']) . '</p></div>';
        }
    }

    /**
     * AJAX测试SMTP连接
     */
    public function ajax_test_smtp() {
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error('权限不足');
        }

        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_test_smtp')) {
            wp_send_json_error('安全验证失败');
        }

        // 测试SMTP连接
        $email_class = WP_Esig_Email::get_instance();
        $result = $email_class->test_smtp_connection();

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
}
