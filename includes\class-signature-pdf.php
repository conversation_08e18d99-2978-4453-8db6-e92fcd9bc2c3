<?php
/**
 * PDF生成类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PDF生成类
 */
class WP_Esig_PDF {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        // 初始化
    }
    
    /**
     * 生成PDF合同
     */
    public function generate_contract_pdf($order, $signature_id) {
        // 获取合同内容
        $contract_content = $this->get_contract_content_for_pdf($order, $signature_id);
        
        if (!$contract_content) {
            return false;
        }
        
        // 创建PDF目录
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/wp-esig-contracts/';
        
        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }
        
        // 生成文件名
        $filename = 'contract_' . $order->get_order_number() . '_' . time() . '.html';
        $file_path = $pdf_dir . $filename;
        
        // 生成完整的HTML文档
        $html_content = $this->generate_html_document($contract_content, $order);
        
        // 保存HTML文件
        if (file_put_contents($file_path, $html_content)) {
            // 如果有PDF转换工具，在这里调用
            // 例如：wkhtmltopdf、puppeteer等
            $pdf_path = $this->convert_html_to_pdf($file_path);
            
            if ($pdf_path) {
                // 删除临时HTML文件
                unlink($file_path);
                return $pdf_path;
            }
            
            // 如果PDF转换失败，返回HTML文件路径
            return $file_path;
        }
        
        return false;
    }

    /**
     * 生成临时合同PDF（不依赖订单）
     */
    public function generate_immediate_contract_pdf($temp_order_data) {
        error_log('WP Electronic Signature: 开始生成临时PDF - 数据: ' . print_r($temp_order_data, true));

        try {
            // 优先使用前端传来的实时HTML内容
            if (!empty($temp_order_data['contract_html'])) {
                error_log('WP Electronic Signature: 使用前端传来的实时HTML内容');
                $contract_content = $temp_order_data['contract_html'];
                error_log('WP Electronic Signature: 实时HTML内容长度: ' . strlen($contract_content));
            } else {
                // 备用方案：重新生成合同内容
                error_log('WP Electronic Signature: 前端HTML内容为空，使用备用方案重新生成');
                $contract_content = $this->get_immediate_contract_content($temp_order_data);

                if (!$contract_content) {
                    error_log('WP Electronic Signature: 获取合同内容失败');
                    error_log('WP Electronic Signature: 临时订单数据详情: ' . print_r($temp_order_data, true));
                    return false;
                }

                error_log('WP Electronic Signature: 备用合同内容获取成功，长度: ' . strlen($contract_content));
            }

            // 创建PDF目录
            $upload_dir = wp_upload_dir();
            if (!$upload_dir || isset($upload_dir['error'])) {
                error_log('WP Electronic Signature: 无法获取上传目录信息');
                return false;
            }

            $pdf_dir = $upload_dir['basedir'] . '/wp-esig-contracts/';

            if (!file_exists($pdf_dir)) {
                $created = wp_mkdir_p($pdf_dir);
                error_log('WP Electronic Signature: 创建PDF目录: ' . $pdf_dir . ' - ' . ($created ? '成功' : '失败'));

                if (!$created) {
                    error_log('WP Electronic Signature: 无法创建PDF目录，权限不足');
                    return false;
                }
            }

            // 验证目录是否可写
            if (!is_writable($pdf_dir)) {
                error_log('WP Electronic Signature: PDF目录不可写: ' . $pdf_dir);
                return false;
            }

            // 生成文件名
            $safe_name = sanitize_file_name($temp_order_data['customer_name']) ?: 'cliente';
            $filename = 'temp_contract_' . $safe_name . '_' . time() . '_' . wp_rand(1000, 9999) . '.html';
            $file_path = $pdf_dir . $filename;

            error_log('WP Electronic Signature: 准备保存文件到: ' . $file_path);

            // 生成完整的HTML文档
            $html_content = $this->generate_immediate_html_document($contract_content, $temp_order_data);

            if (empty($html_content)) {
                error_log('WP Electronic Signature: HTML内容生成失败');
                return false;
            }

            // 保存HTML文件
            $saved = file_put_contents($file_path, $html_content);
            if ($saved === false) {
                error_log('WP Electronic Signature: HTML文件保存失败，可能是权限问题');
                return false;
            }

            error_log('WP Electronic Signature: HTML文件保存成功，大小: ' . $saved . ' 字节');

            // 直接生成PDF（不使用临时HTML文件）
            error_log('WP Electronic Signature: 开始直接生成PDF');

            // 生成持久化PDF文件名
            $safe_name = sanitize_file_name($temp_order_data['customer_name']) ?: 'cliente';
            $timestamp = date('Y-m-d_H-i-s');
            $unique_id = wp_rand(1000, 9999);
            $pdf_filename = 'contract_' . $safe_name . '_' . $timestamp . '_' . $unique_id . '.pdf';
            $pdf_path = $pdf_dir . $pdf_filename;

            error_log('WP Electronic Signature: 目标PDF文件: ' . $pdf_path);

            // 直接从HTML内容生成PDF
            $pdf_generated = $this->generate_pdf_from_html($html_content, $pdf_path);

            if ($pdf_generated) {
                error_log('WP Electronic Signature: PDF生成成功: ' . $pdf_path);

                // 删除临时HTML文件
                @unlink($file_path);

                // 保存合同记录到数据库
                $this->save_contract_record($temp_order_data, $pdf_path);

                return $pdf_path;
            } else {
                error_log('WP Electronic Signature: PDF生成失败');

                // 清理临时文件
                @unlink($file_path);

                return false;
            }

        } catch (Exception $e) {
            error_log('WP Electronic Signature: PDF生成过程中发生异常: ' . $e->getMessage());
            error_log('WP Electronic Signature: 异常堆栈: ' . $e->getTraceAsString());
            error_log('WP Electronic Signature: 异常文件: ' . $e->getFile() . ':' . $e->getLine());

            // 如果有HTML文件，作为备用返回
            if (isset($file_path) && file_exists($file_path)) {
                error_log('WP Electronic Signature: 异常情况下返回HTML文件: ' . $file_path);
                return $file_path;
            }
            return false;
        } catch (Error $e) {
            error_log('WP Electronic Signature: PDF生成过程中发生致命错误: ' . $e->getMessage());
            error_log('WP Electronic Signature: 错误堆栈: ' . $e->getTraceAsString());
            error_log('WP Electronic Signature: 错误文件: ' . $e->getFile() . ':' . $e->getLine());

            // 如果有HTML文件，作为备用返回
            if (isset($file_path) && file_exists($file_path)) {
                error_log('WP Electronic Signature: 致命错误情况下返回HTML文件: ' . $file_path);
                return $file_path;
            }
            return false;
        }
    }

    /**
     * 获取用于PDF的合同内容
     */
    private function get_contract_content_for_pdf($order, $signature_id) {
        $core = WP_Esig_Core::get_instance();
        $database = WP_Esig_Database::get_instance();
        
        // 获取签名记录
        $signature = $database->get_signature_by_id($signature_id);
        
        // 获取客户数据
        $customer_data = array(
            'name' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            'email' => $order->get_billing_email(),
            'phone' => $order->get_billing_phone()
        );
        
        // 获取合同模板内容
        $contract_content = $core->get_dynamic_contract_content($customer_data, $order);
        
        // 添加签名图片
        if ($signature && $signature->signature_image) {
            $signature_path = $this->get_signature_image_path($signature->signature_image);
            if (file_exists($signature_path)) {
                // 将签名图片转换为base64
                $signature_base64 = $this->image_to_base64($signature_path);
                if ($signature_base64) {
                    $signature_img = '<img src="' . $signature_base64 . '" style="width: 200px; height: 100px; border: 1px solid #999;" alt="客户签名">';
                    $contract_content = str_replace('[SIGNATURE_PLACEHOLDER]', $signature_img, $contract_content);
                }
            }
        }
        
        // 移除占位符（如果没有签名）
        $contract_content = str_replace('[SIGNATURE_PLACEHOLDER]', '', $contract_content);
        
        return $contract_content;
    }
    
    /**
     * 生成完整的HTML文档
     */
    private function generate_html_document($content, $order) {
        $html = '<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contrato - Pedido ' . esc_html($order->get_order_number()) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .contract-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
        }
        
        h1, h2, h3 {
            color: #333;
        }
        
        .signature-area {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .signature-area img {
            width: 200px;
            height: 100px;
            border: 1px solid #999;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        td {
            padding: 15px;
            vertical-align: top;
            border: 1px solid #ddd;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .contract-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="contract-container">
        ' . $content . '
    </div>
</body>
</html>';
        
        return $html;
    }
    
    /**
     * 将图片转换为base64
     */
    private function image_to_base64($image_path) {
        if (!file_exists($image_path)) {
            return false;
        }
        
        $image_data = file_get_contents($image_path);
        if (!$image_data) {
            return false;
        }
        
        $image_info = getimagesize($image_path);
        if (!$image_info) {
            return false;
        }
        
        $mime_type = $image_info['mime'];
        $base64 = base64_encode($image_data);
        
        return 'data:' . $mime_type . ';base64,' . $base64;
    }
    
    /**
     * 获取签名图片路径
     */
    private function get_signature_image_path($filename) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['basedir'] . '/wp-esig-signatures/' . $filename;
    }
    
    /**
     * 转换HTML为PDF（专注于mPDF）
     */
    private function convert_html_to_pdf($html_path) {
        error_log('WP Electronic Signature: 开始PDF转换流程，HTML文件: ' . $html_path);

        // 验证HTML文件是否存在
        if (!file_exists($html_path)) {
            error_log('WP Electronic Signature: HTML文件不存在: ' . $html_path);
            return false;
        }

        $html_size = filesize($html_path);
        error_log('WP Electronic Signature: HTML文件大小: ' . $html_size . ' 字节');

        if ($html_size === 0) {
            error_log('WP Electronic Signature: HTML文件为空');
            return false;
        }

        // 直接使用TCPDF进行PDF转换
        error_log('WP Electronic Signature: 开始使用TCPDF进行转换');

        // 读取HTML内容
        $html_content = file_get_contents($html_path);
        if (empty($html_content)) {
            error_log('WP Electronic Signature: HTML内容为空');
            return false;
        }

        // 生成PDF文件路径
        $pdf_path = str_replace('.html', '.pdf', $html_path);

        // 使用TCPDF生成PDF
        if ($this->generate_pdf_with_tcpdf($html_content, $pdf_path)) {
            error_log('WP Electronic Signature: TCPDF转换成功: ' . $pdf_path);
            return $pdf_path;
        }

        error_log('WP Electronic Signature: TCPDF转换失败');
        return false;
    }
    






    /**
     * 将HTML转换为简单文本（保留基本格式）
     */
    private function convert_html_to_simple_text($html_content) {
        // 移除所有HTML标签，但保留基本结构
        $text = strip_tags($html_content);

        // 清理多余的空白
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        // 添加基本的HTML结构用于mPDF
        $simple_html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        h1, h2, h3 { color: #333; }
        .signature-info { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>电子合同</h1>
    <div class="contract-content">
        ' . nl2br(esc_html($text)) . '
    </div>
    <div class="signature-info">
        <p><strong>此合同已通过电子签名系统确认</strong></p>
        <p>生成时间: ' . current_time('Y-m-d H:i:s') . '</p>
    </div>
</body>
</html>';

        return $simple_html;
    }

    /**
     * 将HTML转换为纯文本
     */
    private function html_to_text($html_content) {
        // 移除HTML标签
        $text = strip_tags($html_content);

        // 解码HTML实体
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');

        // 清理多余的空白
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * 创建简单的文本PDF内容
     */
    private function create_simple_text_pdf($text_content) {
        // 这是一个非常基础的PDF结构
        // 在实际应用中，你可能需要使用更完整的PDF库

        $pdf_header = "%PDF-1.4\n";
        $pdf_header .= "1 0 obj\n";
        $pdf_header .= "<<\n";
        $pdf_header .= "/Type /Catalog\n";
        $pdf_header .= "/Pages 2 0 R\n";
        $pdf_header .= ">>\n";
        $pdf_header .= "endobj\n\n";

        $pdf_header .= "2 0 obj\n";
        $pdf_header .= "<<\n";
        $pdf_header .= "/Type /Pages\n";
        $pdf_header .= "/Kids [3 0 R]\n";
        $pdf_header .= "/Count 1\n";
        $pdf_header .= ">>\n";
        $pdf_header .= "endobj\n\n";

        $pdf_content = "3 0 obj\n";
        $pdf_content .= "<<\n";
        $pdf_content .= "/Type /Page\n";
        $pdf_content .= "/Parent 2 0 R\n";
        $pdf_content .= "/MediaBox [0 0 612 792]\n";
        $pdf_content .= "/Contents 4 0 R\n";
        $pdf_content .= "/Resources <<\n";
        $pdf_content .= "/Font <<\n";
        $pdf_content .= "/F1 <<\n";
        $pdf_content .= "/Type /Font\n";
        $pdf_content .= "/Subtype /Type1\n";
        $pdf_content .= "/BaseFont /Helvetica\n";
        $pdf_content .= ">>\n";
        $pdf_content .= ">>\n";
        $pdf_content .= ">>\n";
        $pdf_content .= ">>\n";
        $pdf_content .= "endobj\n\n";

        // 简化文本内容，避免特殊字符问题
        $safe_text = preg_replace('/[^\x20-\x7E]/', '?', $text_content);
        $safe_text = substr($safe_text, 0, 1000); // 限制长度

        $pdf_content .= "4 0 obj\n";
        $pdf_content .= "<<\n";
        $pdf_content .= "/Length " . (strlen($safe_text) + 50) . "\n";
        $pdf_content .= ">>\n";
        $pdf_content .= "stream\n";
        $pdf_content .= "BT\n";
        $pdf_content .= "/F1 12 Tf\n";
        $pdf_content .= "50 750 Td\n";
        $pdf_content .= "(" . $safe_text . ") Tj\n";
        $pdf_content .= "ET\n";
        $pdf_content .= "endstream\n";
        $pdf_content .= "endobj\n\n";

        $pdf_footer = "xref\n";
        $pdf_footer .= "0 5\n";
        $pdf_footer .= "0000000000 65535 f \n";
        $pdf_footer .= "0000000009 65535 n \n";
        $pdf_footer .= "0000000074 65535 n \n";
        $pdf_footer .= "0000000120 65535 n \n";
        $pdf_footer .= "0000000274 65535 n \n";
        $pdf_footer .= "trailer\n";
        $pdf_footer .= "<<\n";
        $pdf_footer .= "/Size 5\n";
        $pdf_footer .= "/Root 1 0 R\n";
        $pdf_footer .= ">>\n";
        $pdf_footer .= "startxref\n";
        $pdf_footer .= strlen($pdf_header . $pdf_content) . "\n";
        $pdf_footer .= "%%EOF\n";

        return $pdf_header . $pdf_content . $pdf_footer;
    }

    /**
     * 为TCPDF准备HTML内容
     */
    private function prepare_html_for_tcpdf($html_content) {
        error_log('WP Electronic Signature: 开始为TCPDF准备HTML内容');
        error_log('WP Electronic Signature: 原始HTML长度: ' . strlen($html_content));

        // TCPDF对HTML的支持有限，需要简化处理

        // 移除复杂的CSS样式
        $html_content = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html_content);
        error_log('WP Electronic Signature: 移除CSS后长度: ' . strlen($html_content));

        // 移除JavaScript
        $html_content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html_content);
        error_log('WP Electronic Signature: 移除JS后长度: ' . strlen($html_content));

        // 处理base64图片 - 使用TCPDF兼容的样式
        error_log('WP Electronic Signature: 开始处理base64图片');
        $image_count = 0;
        $html_content = preg_replace_callback(
            '/<img[^>]*src="((?:data:)?image\/[^"]+)"[^>]*>/i',
            function($matches) use (&$image_count) {
                $image_count++;
                error_log('WP Electronic Signature: 处理第' . $image_count . '个base64图片');

                // 尝试将base64图片转换为临时文件
                $base64_data = $matches[1];
                error_log('WP Electronic Signature: base64数据长度: ' . strlen($base64_data));

                $temp_image_path = $this->save_base64_image_for_tcpdf($base64_data);

                if ($temp_image_path) {
                    error_log('WP Electronic Signature: base64图片转换成功: ' . $temp_image_path);
                    return '<div style="width: 100%; text-align: center; padding: 10px;"><img src="' . $temp_image_path . '" style="width: 250px; height: 100px; border: 1px solid #999;" /></div>';
                } else {
                    error_log('WP Electronic Signature: base64图片转换失败，使用文本替代');
                    // 如果图片处理失败，用文本替代
                    return '<div style="width: 100%; text-align: center; padding: 20px; color: #666; font-size: 12px; border: 1px solid #ccc; background: #f9f9f9;"><strong>[电子签名已确认]</strong></div>';
                }
            },
            $html_content
        );
        error_log('WP Electronic Signature: 共处理了' . $image_count . '个base64图片');

        // 简化HTML结构 - 保持原有结构但优化样式
        $html_content = preg_replace('/<div[^>]*class="[^"]*signature-section[^"]*"[^>]*>/i', '<div style="margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9;">', $html_content);

        // 确保基本HTML结构
        if (strpos($html_content, '<html') === false) {
            $html_content = '<html><head><meta charset="UTF-8"></head><body>' . $html_content . '</body></html>';
        }

        // 添加基本样式 - 优化签名对齐
        $tcpdf_styles = '<style>
            body { font-family: helvetica, sans-serif; font-size: 12pt; line-height: 1.4; }
            h1, h2, h3 { color: #333; margin: 10px 0; text-align: center; }
            p { margin: 5px 0; }
            table { width: 100%; border-collapse: collapse; }
            td { padding: 15px; border: 1px solid #ccc; vertical-align: top; }
            img { display: block; margin: 0 auto; }
            .signature-info { margin: 15px 0; padding: 10px; border: 1px solid #ccc; }
        </style>';

        $html_content = str_replace('<head>', '<head>' . $tcpdf_styles, $html_content);

        error_log('WP Electronic Signature: TCPDF HTML内容准备完成');
        return $html_content;
    }

    /**
     * 为TCPDF保存base64图片为临时文件
     */
    private function save_base64_image_for_tcpdf($base64_data) {
        try {
            // 处理没有data:前缀的情况
            if (strpos($base64_data, 'data:') !== 0) {
                $base64_data = 'data:' . $base64_data;
            }

            // 解析base64数据
            $data_parts = explode(',', $base64_data);
            if (count($data_parts) !== 2) {
                return false;
            }

            $header = $data_parts[0];
            $data = $data_parts[1];

            // 获取图片类型
            if (strpos($header, 'image/png') !== false) {
                $extension = 'png';
            } elseif (strpos($header, 'image/jpeg') !== false) {
                $extension = 'jpg';
            } else {
                return false;
            }

            // 解码base64数据
            $image_data = base64_decode($data);
            if ($image_data === false) {
                return false;
            }

            // 创建临时文件
            $upload_dir = wp_upload_dir();
            $temp_dir = $upload_dir['basedir'] . '/wp-esig-temp/';
            wp_mkdir_p($temp_dir);

            $temp_filename = 'tcpdf_signature_' . time() . '_' . wp_rand(1000, 9999) . '.' . $extension;
            $temp_file_path = $temp_dir . $temp_filename;

            // 保存图片文件
            if (file_put_contents($temp_file_path, $image_data)) {
                error_log('WP Electronic Signature: TCPDF临时图片保存成功: ' . $temp_file_path);
                return $temp_file_path;
            }

            return false;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: TCPDF图片保存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 错误后清理资源
     */
    private function cleanup_after_error($original_memory_limit, $original_time_limit, $html_path) {
        // 恢复原始设置
        if (isset($original_memory_limit)) {
            ini_set('memory_limit', $original_memory_limit);
        }
        if (isset($original_time_limit)) {
            set_time_limit($original_time_limit);
        }

        // 清理临时文件
        if (isset($html_path) && file_exists($html_path)) {
            @unlink($html_path);
        }
    }




    
    /**
     * 获取PDF文件URL
     */
    public function get_pdf_url($pdf_path) {
        $upload_dir = wp_upload_dir();
        $pdf_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $pdf_path);
        return $pdf_url;
    }
    
    /**
     * 清理旧的PDF文件
     */
    public function cleanup_old_pdfs($days = 30) {
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/wp-esig-contracts/';
        
        if (!is_dir($pdf_dir)) {
            return;
        }
        
        $files = glob($pdf_dir . '*');
        $cutoff_time = time() - ($days * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoff_time) {
                unlink($file);
            }
        }
    }

    /**
     * 获取临时合同内容（用户签名后的完整预览内容）
     */
    private function get_immediate_contract_content($temp_order_data) {
        error_log('WP Electronic Signature: 开始获取用户签名后的完整合同内容');

        // 验证输入数据
        if (empty($temp_order_data) || !is_array($temp_order_data)) {
            error_log('WP Electronic Signature: 临时订单数据为空或格式错误');
            return false;
        }

        // 检查必要字段（phone字段可以为空）
        $required_fields = ['customer_name', 'customer_email'];
        foreach ($required_fields as $field) {
            if (empty($temp_order_data[$field])) {
                error_log('WP Electronic Signature: 缺少必要字段: ' . $field);
                return false;
            }
        }

        // 确保phone字段存在，即使为空
        if (!isset($temp_order_data['customer_phone'])) {
            $temp_order_data['customer_phone'] = '';
        }

        try {
            $core = WP_Esig_Core::get_instance();
            if (!$core) {
                error_log('WP Electronic Signature: 无法获取核心实例');
                return false;
            }

            // 创建模拟的客户数据用于获取动态内容
            $customer_data = array(
                'name' => $temp_order_data['customer_name'],
                'email' => $temp_order_data['customer_email'],
                'phone' => isset($temp_order_data['customer_phone']) ? $temp_order_data['customer_phone'] : ''
            );

            error_log('WP Electronic Signature: 准备获取动态合同内容，客户数据: ' . print_r($customer_data, true));

            // 获取动态合同内容（这是用户在前端看到的内容）
            $content = $core->get_dynamic_contract_content($customer_data, null);

            if (!$content) {
                error_log('WP Electronic Signature: 无法获取动态合同内容');
                return false;
            }

            error_log('WP Electronic Signature: 动态合同内容获取成功，长度: ' . strlen($content));

        } catch (Exception $e) {
            error_log('WP Electronic Signature: 获取合同内容时发生异常: ' . $e->getMessage());
            error_log('WP Electronic Signature: 异常堆栈: ' . $e->getTraceAsString());
            return false;
        } catch (Error $e) {
            error_log('WP Electronic Signature: 获取合同内容时发生致命错误: ' . $e->getMessage());
            error_log('WP Electronic Signature: 错误堆栈: ' . $e->getTraceAsString());
            return false;
        }

        // 处理签名区域 - 将签名占位符替换为实际签名
        if (!empty($temp_order_data['signature_data'])) {
            error_log('WP Electronic Signature: 开始处理签名图片');

            // 直接使用base64格式（mPDF兼容性更好）
            $signature_base64 = $this->validate_and_optimize_signature_base64($temp_order_data['signature_data']);

            if ($signature_base64) {
                error_log('WP Electronic Signature: 签名base64验证成功');

                // 创建签名HTML（使用TCPDF兼容的样式，保持div结构）
                $signature_html = '<div style="margin-top: 30px; padding: 20px; border: 1px solid #ddd; background: #f9f9f9;">';
                $signature_html .= '<h3 style="margin: 0 0 15px 0; color: #333; text-align: center;">电子签名</h3>';
                $signature_html .= '<div style="margin-bottom: 15px;">';
                $signature_html .= '<p style="margin: 5px 0;"><strong>签署人：</strong> ' . esc_html($temp_order_data['customer_name']) . '</p>';
                $signature_html .= '<p style="margin: 5px 0;"><strong>CPF：</strong> ' . esc_html($temp_order_data['customer_cpf']) . '</p>';
                $signature_html .= '<p style="margin: 5px 0;"><strong>邮箱：</strong> ' . esc_html($temp_order_data['customer_email']) . '</p>';
                $signature_html .= '<p style="margin: 5px 0;"><strong>签署时间：</strong> ' . current_time('Y-m-d H:i:s') . '</p>';
                $signature_html .= '</div>';
                $signature_html .= '<div style="text-align: center; padding: 10px; border: 1px solid #ccc; background: #fff;">';
                $signature_html .= '<p style="margin: 0 0 10px 0;"><strong>签名：</strong></p>';
                $signature_html .= '<img src="' . $signature_base64 . '" alt="客户签名" style="width: 250px; height: 100px; border: 1px solid #999; padding: 5px; background: #fff;">';
                $signature_html .= '</div>';
                $signature_html .= '</div>';
            } else {
                error_log('WP Electronic Signature: 签名base64验证失败，使用文本替代');

                // 如果签名图片处理失败，使用文本替代（保持div结构）
                $signature_html = '<div style="margin-top: 30px; padding: 20px; border: 1px solid #ddd; background: #f9f9f9;">';
                $signature_html .= '<h3 style="margin: 0 0 15px 0; color: #333; text-align: center;">电子签名</h3>';
                $signature_html .= '<div style="margin-bottom: 15px;">';
                $signature_html .= '<p style="margin: 5px 0;"><strong>签署人：</strong> ' . esc_html($temp_order_data['customer_name']) . '</p>';
                $signature_html .= '<p style="margin: 5px 0;"><strong>CPF：</strong> ' . esc_html($temp_order_data['customer_cpf']) . '</p>';
                $signature_html .= '<p style="margin: 5px 0;"><strong>邮箱：</strong> ' . esc_html($temp_order_data['customer_email']) . '</p>';
                $signature_html .= '<p style="margin: 5px 0;"><strong>签署时间：</strong> ' . current_time('Y-m-d H:i:s') . '</p>';
                $signature_html .= '</div>';
                $signature_html .= '<div style="text-align: center; padding: 20px; border: 1px solid #ccc; background: #fff; height: 100px;">';
                $signature_html .= '<p style="margin: 10px 0;"><strong>[电子签名已确认]</strong></p>';
                $signature_html .= '<p style="margin: 10px 0; font-style: italic;">签名人: ' . esc_html($temp_order_data['customer_name']) . '</p>';
                $signature_html .= '</div>';
                $signature_html .= '</div>';
            }

            // 查找并替换签名占位符
            if (strpos($content, '[SIGNATURE_PLACEHOLDER]') !== false) {
                $content = str_replace('[SIGNATURE_PLACEHOLDER]', $signature_html, $content);
            } else if (strpos($content, 'wp-esig-buyer-signature-area') !== false) {
                // 如果有买家签名区域，替换其内容
                $content = preg_replace(
                    '/<div[^>]*id="wp-esig-buyer-signature-area"[^>]*>.*?<\/div>/s',
                    '<div id="wp-esig-buyer-signature-area">' . $signature_html . '</div>',
                    $content
                );
            } else {
                // 如果没有找到占位符，在内容末尾添加签名
                $content .= $signature_html;
            }
        }

        // 清理可能残留的前端交互元素
        $content = $this->clean_frontend_elements($content);

        error_log('WP Electronic Signature: 签名后合同内容处理完成，最终长度: ' . strlen($content));

        return $content;
    }

    /**
     * 清理前端交互元素
     */
    private function clean_frontend_elements($content) {
        // 移除前端JavaScript相关的元素
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);

        // 移除签名画板相关元素
        $content = preg_replace('/<canvas[^>]*id="wp-esig-signature-pad"[^>]*>.*?<\/canvas>/s', '', $content);
        $content = preg_replace('/<div[^>]*id="wp-esig-signature-controls"[^>]*>.*?<\/div>/s', '', $content);
        $content = preg_replace('/<div[^>]*id="wp-esig-signature-placeholder"[^>]*>.*?<\/div>/s', '', $content);

        // 移除其他可能的交互元素
        $content = preg_replace('/<button[^>]*>.*?<\/button>/s', '', $content);
        $content = preg_replace('/<input[^>]*type="button"[^>]*>/i', '', $content);

        return $content;
    }

    /**
     * 生成临时HTML文档
     */
    private function generate_immediate_html_document($content, $temp_order_data) {
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子合同 - ' . esc_html($temp_order_data['customer_name']) . '</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .contract-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .contract-content {
            margin-bottom: 30px;
        }
        .signature-area {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .signature-area img {
            display: block;
            margin: 10px 0;
        }
        .contract-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="contract-header">
        <h1>电子合同</h1>
        <p>生成时间：' . current_time('Y-m-d H:i:s') . '</p>
    </div>

    <div class="contract-content">
        ' . $content . '
    </div>

    <div class="contract-footer">
        <p>此合同由 ' . get_bloginfo('name') . ' 电子签名系统生成</p>
        <p>客户：' . esc_html($temp_order_data['customer_name']) . ' | CPF：' . esc_html($temp_order_data['customer_cpf']) . '</p>
    </div>
</body>
</html>';

        return $html;
    }

    /**
     * 使用HTML→图片→PDF的方式生成PDF（更可靠）
     */
    private function generate_pdf_from_html($html_content, $pdf_path) {
        error_log('WP Electronic Signature: 开始使用TCPDF生成PDF');
        error_log('WP Electronic Signature: HTML内容长度: ' . strlen($html_content));
        error_log('WP Electronic Signature: 目标PDF路径: ' . $pdf_path);

        try {
            // 检查TCPDF是否可用
            if (!class_exists('TCPDF')) {
                error_log('WP Electronic Signature: TCPDF类不可用，尝试加载');

                // 尝试加载TCPDF
                $tcpdf_path = ABSPATH . 'wp-content/plugins/wp-electronic-signature/vendor/tecnickcom/tcpdf/tcpdf.php';
                if (file_exists($tcpdf_path)) {
                    require_once($tcpdf_path);
                    error_log('WP Electronic Signature: TCPDF加载成功');
                } else {
                    error_log('WP Electronic Signature: TCPDF文件不存在: ' . $tcpdf_path);
                    return false;
                }
            }

            // 主要方案：使用TCPDF生成PDF
            error_log('WP Electronic Signature: 开始尝试TCPDF生成');
            $tcpdf_result = $this->generate_pdf_with_tcpdf($html_content, $pdf_path);
            error_log('WP Electronic Signature: TCPDF生成结果: ' . ($tcpdf_result ? 'success' : 'failed'));

            if ($tcpdf_result) {
                error_log('WP Electronic Signature: TCPDF生成成功');
                return true;
            }

            // 备用方案：生成HTML文件作为备用（但这不是PDF）
            error_log('WP Electronic Signature: TCPDF失败，尝试HTML备用方案');
            if ($this->generate_html_backup($html_content, $pdf_path)) {
                error_log('WP Electronic Signature: HTML备用文件生成成功，但这不是PDF文件');
                // 注意：这里返回false，因为我们需要PDF文件，不是HTML
                return false;
            }

            error_log('WP Electronic Signature: 所有PDF生成方案都失败了');
            return false;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: PDF生成异常: ' . $e->getMessage());
            error_log('WP Electronic Signature: 异常文件: ' . $e->getFile() . ':' . $e->getLine());
            error_log('WP Electronic Signature: 异常堆栈: ' . $e->getTraceAsString());
            return false;
        } catch (Error $e) {
            error_log('WP Electronic Signature: PDF生成致命错误: ' . $e->getMessage());
            error_log('WP Electronic Signature: 错误文件: ' . $e->getFile() . ':' . $e->getLine());
            error_log('WP Electronic Signature: 错误堆栈: ' . $e->getTraceAsString());
            return false;
        }
    }



    /**
     * 生成HTML备用文件（当TCPDF失败时的备用方案）
     */
    private function generate_html_backup($html_content, $pdf_path) {
        error_log('WP Electronic Signature: 尝试生成HTML备用文件');

        try {
            // 将HTML转换为纯文本
            $text_content = $this->html_to_text($html_content);

            // 创建一个简单的HTML文件作为备用
            $simple_html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>电子合同</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .content { margin-bottom: 30px; }
        .signature { margin-top: 30px; padding: 20px; border: 1px solid #ccc; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="header">
        <h1>电子合同</h1>
        <p>生成时间: ' . current_time('Y-m-d H:i:s') . '</p>
    </div>
    <div class="content">
        ' . $html_content . '
    </div>
    <div class="signature">
        <p><strong>此合同已通过电子签名系统确认</strong></p>
        <p>注意：由于技术原因，此文件以HTML格式提供，但合同内容和签名完全有效。</p>
    </div>
</body>
</html>';

            // 保存为HTML文件（作为PDF的备用）
            $html_path = str_replace('.pdf', '_backup.html', $pdf_path);
            $saved = file_put_contents($html_path, $simple_html);

            if ($saved && file_exists($html_path) && filesize($html_path) > 100) {
                error_log('WP Electronic Signature: 备用HTML文件生成成功: ' . $html_path);

                // 将HTML文件路径作为PDF路径返回（邮件系统会处理）
                if (copy($html_path, $pdf_path)) {
                    error_log('WP Electronic Signature: 备用文件复制成功');
                    return true;
                }
            }

            return false;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: HTML备用文件生成失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用TCPDF生成PDF
     */
    private function generate_pdf_with_tcpdf($html_content, $pdf_path) {
        error_log('WP Electronic Signature: 尝试使用TCPDF生成PDF');
        error_log('WP Electronic Signature: 输入HTML长度: ' . strlen($html_content));
        error_log('WP Electronic Signature: 目标文件路径: ' . $pdf_path);

        try {
            // 检查TCPDF是否可用
            $tcpdf_path = WP_ESIG_PLUGIN_DIR . 'includes/tcpdf/tcpdf.php';
            error_log('WP Electronic Signature: 查找TCPDF文件: ' . $tcpdf_path);

            if (!file_exists($tcpdf_path)) {
                error_log('WP Electronic Signature: TCPDF文件不存在: ' . $tcpdf_path);

                // 尝试其他可能的路径
                $alternative_paths = array(
                    WP_ESIG_PLUGIN_DIR . 'vendor/tecnickcom/tcpdf/tcpdf.php',
                    ABSPATH . 'wp-content/plugins/wp-electronic-signature/vendor/tecnickcom/tcpdf/tcpdf.php'
                );

                foreach ($alternative_paths as $alt_path) {
                    error_log('WP Electronic Signature: 尝试备用路径: ' . $alt_path);
                    if (file_exists($alt_path)) {
                        $tcpdf_path = $alt_path;
                        error_log('WP Electronic Signature: 找到TCPDF文件: ' . $tcpdf_path);
                        break;
                    }
                }

                if (!file_exists($tcpdf_path)) {
                    error_log('WP Electronic Signature: 所有TCPDF路径都不存在');
                    return false;
                }
            }

            // 加载TCPDF
            error_log('WP Electronic Signature: 开始加载TCPDF文件');
            require_once $tcpdf_path;

            if (!class_exists('TCPDF')) {
                error_log('WP Electronic Signature: TCPDF类加载失败');
                return false;
            }

            error_log('WP Electronic Signature: TCPDF加载成功，开始生成PDF');

            // 创建TCPDF实例
            error_log('WP Electronic Signature: 开始创建TCPDF实例');
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
            error_log('WP Electronic Signature: TCPDF实例创建成功');

            // 设置文档信息
            $pdf->SetCreator('WP Electronic Signature');
            $pdf->SetAuthor('WP Electronic Signature Plugin');
            $pdf->SetTitle('电子合同');
            $pdf->SetSubject('电子签名合同');

            // 设置页边距
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // 添加页面
            $pdf->AddPage();

            // 设置字体
            $pdf->SetFont('helvetica', '', 12);

            // 将HTML转换为TCPDF可以处理的格式
            error_log('WP Electronic Signature: 开始准备HTML内容给TCPDF');
            $tcpdf_html = $this->prepare_html_for_tcpdf($html_content);
            error_log('WP Electronic Signature: HTML准备完成，处理后长度: ' . strlen($tcpdf_html));

            // 写入HTML内容
            error_log('WP Electronic Signature: 开始写入HTML内容到PDF');
            try {
                $pdf->writeHTML($tcpdf_html, true, false, true, false, '');
                error_log('WP Electronic Signature: HTML内容写入成功');
            } catch (Exception $e) {
                error_log('WP Electronic Signature: HTML写入失败: ' . $e->getMessage());
                return false;
            }

            // 输出PDF
            error_log('WP Electronic Signature: 开始输出PDF文件到: ' . $pdf_path);
            try {
                $pdf->Output($pdf_path, 'F');
                error_log('WP Electronic Signature: PDF输出完成');
            } catch (Exception $e) {
                error_log('WP Electronic Signature: PDF输出失败: ' . $e->getMessage());
                return false;
            }

            // 验证文件
            error_log('WP Electronic Signature: 开始验证生成的PDF文件');
            if (file_exists($pdf_path)) {
                $file_size = filesize($pdf_path);
                error_log('WP Electronic Signature: PDF文件存在，大小: ' . $file_size . ' 字节');

                if ($file_size > 100) {
                    error_log('WP Electronic Signature: TCPDF生成成功');
                    return true;
                } else {
                    error_log('WP Electronic Signature: PDF文件过小，可能生成失败');
                    return false;
                }
            } else {
                error_log('WP Electronic Signature: PDF文件不存在: ' . $pdf_path);
                return false;
            }

        } catch (Exception $e) {
            error_log('WP Electronic Signature: TCPDF生成失败: ' . $e->getMessage());
            error_log('WP Electronic Signature: 错误文件: ' . $e->getFile() . ':' . $e->getLine());
            return false;
        }
    }

    /**
     * 使用TCPDF生成PDF（完整版本，如果需要的话）
     */
    private function generate_pdf_with_tcpdf_full($html_content, $pdf_path) {
        error_log('WP Electronic Signature: 尝试使用TCPDF生成PDF');

        try {
            // 检查TCPDF是否可用
            if (!class_exists('TCPDF')) {
                error_log('WP Electronic Signature: TCPDF类不可用');
                return false;
            }

            // 创建TCPDF实例
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

            // 设置文档信息
            $pdf->SetCreator('WP Electronic Signature');
            $pdf->SetTitle('电子合同');
            $pdf->SetSubject('电子签名合同');

            // 设置页边距
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // 添加页面
            $pdf->AddPage();

            // 设置字体
            $pdf->SetFont('helvetica', '', 12);

            // 将HTML转换为简单文本
            $text_content = $this->html_to_text($html_content);

            // 写入纯文本内容
            $pdf->Write(0, $text_content, '', 0, 'L', true, 0, false, false, 0);

            // 输出PDF
            $pdf->Output($pdf_path, 'F');

            // 验证文件
            if (file_exists($pdf_path) && filesize($pdf_path) > 100) {
                error_log('WP Electronic Signature: TCPDF生成成功');
                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: TCPDF生成失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用HTML→图片→PDF方式（简化版本）
     */
    private function generate_pdf_via_image($html_content, $pdf_path) {
        error_log('WP Electronic Signature: HTML→图片→PDF方式暂不可用，跳过');
        return false;
    }

    /**
     * 生成简化版PDF（最后的备用方案）
     */
    private function generate_simple_pdf($html_content, $pdf_path) {
        error_log('WP Electronic Signature: 简化版PDF生成器暂不可用，跳过');
        return false;
    }

    /**
     * 保存合同记录到数据库
     */
    private function save_contract_record($temp_order_data, $pdf_path) {
        global $wpdb;

        try {
            $table_name = $wpdb->prefix . 'esig_contract_records';

            // 创建合同记录表（如果不存在）
            $this->create_contract_records_table();

            // 插入记录
            $result = $wpdb->insert(
                $table_name,
                array(
                    'customer_name' => $temp_order_data['customer_name'],
                    'customer_email' => $temp_order_data['customer_email'],
                    'customer_cpf' => isset($temp_order_data['customer_cpf']) ? $temp_order_data['customer_cpf'] : '',
                    'customer_phone' => isset($temp_order_data['customer_phone']) ? $temp_order_data['customer_phone'] : '',
                    'pdf_path' => $pdf_path,
                    'pdf_filename' => basename($pdf_path),
                    'signed_at' => current_time('mysql'),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                    'status' => 'completed',
                    'created_at' => current_time('mysql')
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
            );

            if ($result) {
                error_log('WP Electronic Signature: 合同记录保存成功，ID: ' . $wpdb->insert_id);
            } else {
                error_log('WP Electronic Signature: 合同记录保存失败: ' . $wpdb->last_error);
            }

        } catch (Exception $e) {
            error_log('WP Electronic Signature: 保存合同记录异常: ' . $e->getMessage());
        }
    }

    /**
     * 创建合同记录表
     */
    private function create_contract_records_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_records';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            customer_name varchar(255) NOT NULL,
            customer_email varchar(255) NOT NULL,
            customer_cpf varchar(20),
            customer_phone varchar(20),
            pdf_path varchar(500) NOT NULL,
            pdf_filename varchar(255) NOT NULL,
            signed_at datetime NOT NULL,
            ip_address varchar(45),
            user_agent text,
            status varchar(20) DEFAULT 'completed',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY customer_email (customer_email),
            KEY signed_at (signed_at),
            KEY status (status)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * 验证和优化签名base64数据
     */
    private function validate_and_optimize_signature_base64($signature_data) {
        try {
            // 验证base64数据格式
            if (strpos($signature_data, 'data:image/') !== 0) {
                error_log('WP Electronic Signature: 签名数据格式不正确');
                return false;
            }

            // 解析base64数据
            $data_parts = explode(',', $signature_data);
            if (count($data_parts) !== 2) {
                error_log('WP Electronic Signature: base64数据解析失败');
                return false;
            }

            $header = $data_parts[0];
            $data = $data_parts[1];

            // 验证支持的图片格式
            if (strpos($header, 'image/png') === false && strpos($header, 'image/jpeg') === false) {
                error_log('WP Electronic Signature: 不支持的图片格式: ' . $header);
                return false;
            }

            // 验证base64数据是否有效
            $image_data = base64_decode($data, true);
            if ($image_data === false) {
                error_log('WP Electronic Signature: base64解码失败');
                return false;
            }

            // 验证图片数据是否有效
            $image_info = @getimagesizefromstring($image_data);
            if ($image_info === false) {
                error_log('WP Electronic Signature: 图片数据无效');
                return false;
            }

            // 检查图片尺寸是否合理
            if ($image_info[0] > 2000 || $image_info[1] > 1000) {
                error_log('WP Electronic Signature: 签名图片尺寸过大: ' . $image_info[0] . 'x' . $image_info[1]);
                // 可以选择压缩图片，这里先返回原数据
            }

            // 检查文件大小
            $data_size = strlen($image_data);
            if ($data_size > 500000) { // 500KB
                error_log('WP Electronic Signature: 签名图片文件过大: ' . $data_size . ' 字节');
                // 可以选择压缩图片，这里先返回原数据
            }

            error_log('WP Electronic Signature: 签名base64验证成功，尺寸: ' . $image_info[0] . 'x' . $image_info[1] . ', 大小: ' . $data_size . ' 字节');

            // 返回原始的base64数据（包含data:image/前缀）
            return $signature_data;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: 验证签名base64异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 将base64签名图片保存为临时文件（备用方法）
     */
    private function save_signature_as_temp_file($signature_data) {
        try {
            // 验证base64数据
            if (strpos($signature_data, 'data:image/') !== 0) {
                error_log('WP Electronic Signature: 签名数据格式不正确');
                return false;
            }

            // 解析base64数据
            $data_parts = explode(',', $signature_data);
            if (count($data_parts) !== 2) {
                error_log('WP Electronic Signature: base64数据解析失败');
                return false;
            }

            $header = $data_parts[0];
            $data = $data_parts[1];

            // 获取图片类型
            if (strpos($header, 'image/png') !== false) {
                $extension = 'png';
            } elseif (strpos($header, 'image/jpeg') !== false) {
                $extension = 'jpg';
            } else {
                error_log('WP Electronic Signature: 不支持的图片格式');
                return false;
            }

            // 解码base64数据
            $image_data = base64_decode($data);
            if ($image_data === false) {
                error_log('WP Electronic Signature: base64解码失败');
                return false;
            }

            // 创建临时文件
            $upload_dir = wp_upload_dir();
            $temp_dir = $upload_dir['basedir'] . '/wp-esig-temp/';
            wp_mkdir_p($temp_dir);

            $temp_filename = 'signature_' . time() . '_' . wp_rand(1000, 9999) . '.' . $extension;
            $temp_file_path = $temp_dir . $temp_filename;

            // 保存图片文件
            $saved = file_put_contents($temp_file_path, $image_data);
            if ($saved === false) {
                error_log('WP Electronic Signature: 签名图片保存失败');
                return false;
            }

            // 验证文件
            if (!file_exists($temp_file_path) || filesize($temp_file_path) === 0) {
                error_log('WP Electronic Signature: 签名图片文件无效');
                return false;
            }

            error_log('WP Electronic Signature: 签名图片保存成功: ' . $temp_file_path . ', 大小: ' . filesize($temp_file_path) . ' 字节');
            return $temp_file_path;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: 保存签名图片异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成测试PDF（用于调试HTML转PDF功能）
     */
    public function generate_test_pdf($test_data) {
        $debug_info = array();
        return $this->generate_test_pdf_with_debug($test_data, $debug_info);
    }

    /**
     * 生成测试PDF并收集调试信息
     */
    public function generate_test_pdf_with_debug($test_data, &$debug_info) {
        $debug_info[] = '开始生成测试PDF';

        try {
            // 确保上传目录存在
            $upload_dir = wp_upload_dir();
            $pdf_dir = $upload_dir['basedir'] . '/wp-esig-pdfs/';
            $debug_info[] = 'PDF目录: ' . $pdf_dir;

            if (!file_exists($pdf_dir)) {
                wp_mkdir_p($pdf_dir);
                $debug_info[] = '创建PDF目录';
            } else {
                $debug_info[] = 'PDF目录已存在';
            }

            // 使用前端传来的实时HTML内容
            if (!empty($test_data['contract_html'])) {
                $contract_content = $test_data['contract_html'];
                $debug_info[] = '使用前端HTML内容，长度: ' . strlen($contract_content);
            } else {
                $debug_info[] = '❌ HTML内容为空';
                return false;
            }

            // 生成测试PDF文件名
            $safe_name = sanitize_file_name($test_data['customer_name']) ?: 'test_user';
            $timestamp = date('Y-m-d_H-i-s');
            $unique_id = wp_rand(1000, 9999);
            $pdf_filename = 'test_contract_' . $safe_name . '_' . $timestamp . '_' . $unique_id . '.pdf';
            $pdf_path = $pdf_dir . $pdf_filename;
            $debug_info[] = '目标PDF文件: ' . $pdf_filename;

            // 直接从HTML内容生成PDF
            $debug_info[] = '开始调用PDF生成方法';
            $pdf_generated = $this->generate_pdf_from_html_with_debug($contract_content, $pdf_path, $debug_info);
            $debug_info[] = 'PDF生成方法返回: ' . ($pdf_generated ? 'true' : 'false');

            if ($pdf_generated) {
                if (file_exists($pdf_path)) {
                    $file_size = filesize($pdf_path);
                    $debug_info[] = '✅ PDF文件已创建，大小: ' . $file_size . ' 字节';

                    if ($file_size > 100) {
                        $debug_info[] = '✅ 测试PDF生成成功';
                        return $pdf_path;
                    } else {
                        $debug_info[] = '❌ PDF文件过小，可能生成失败';
                        return false;
                    }
                } else {
                    $debug_info[] = '❌ PDF文件不存在';
                    return false;
                }
            } else {
                $debug_info[] = '❌ PDF生成方法返回失败';
                return false;
            }

        } catch (Exception $e) {
            $debug_info[] = '❌ 异常: ' . $e->getMessage();
            $debug_info[] = '异常文件: ' . $e->getFile() . ':' . $e->getLine();
            return false;
        } catch (Error $e) {
            $debug_info[] = '❌ 致命错误: ' . $e->getMessage();
            $debug_info[] = '错误文件: ' . $e->getFile() . ':' . $e->getLine();
            return false;
        }
    }

    /**
     * 生成PDF并收集调试信息
     */
    private function generate_pdf_from_html_with_debug($html_content, $pdf_path, &$debug_info) {
        $debug_info[] = '开始HTML转PDF过程';
        $debug_info[] = 'HTML内容长度: ' . strlen($html_content);
        $debug_info[] = '目标PDF路径: ' . $pdf_path;

        try {
            // 检查TCPDF是否可用
            $tcpdf_path = WP_ESIG_PLUGIN_DIR . 'includes/tcpdf/tcpdf.php';
            $debug_info[] = '查找TCPDF文件: ' . $tcpdf_path;

            if (!file_exists($tcpdf_path)) {
                $debug_info[] = '❌ TCPDF文件不存在，尝试备用路径';

                // 尝试其他可能的路径
                $alternative_paths = array(
                    WP_ESIG_PLUGIN_DIR . 'vendor/tecnickcom/tcpdf/tcpdf.php',
                    ABSPATH . 'wp-content/plugins/wp-electronic-signature/vendor/tecnickcom/tcpdf/tcpdf.php'
                );

                $found = false;
                foreach ($alternative_paths as $alt_path) {
                    $debug_info[] = '尝试备用路径: ' . $alt_path;
                    if (file_exists($alt_path)) {
                        $tcpdf_path = $alt_path;
                        $debug_info[] = '✅ 找到TCPDF文件: ' . $tcpdf_path;
                        $found = true;
                        break;
                    }
                }

                if (!$found) {
                    $debug_info[] = '❌ 所有TCPDF路径都不存在';
                    return false;
                }
            } else {
                $debug_info[] = '✅ TCPDF文件存在';
            }

            // 加载TCPDF
            $debug_info[] = '开始加载TCPDF';
            require_once $tcpdf_path;

            if (!class_exists('TCPDF')) {
                $debug_info[] = '❌ TCPDF类加载失败';
                return false;
            }

            $debug_info[] = '✅ TCPDF类加载成功';

            // 创建TCPDF实例
            $debug_info[] = '创建TCPDF实例';
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
            $debug_info[] = '✅ TCPDF实例创建成功';

            // 设置文档信息
            $pdf->SetCreator('WP Electronic Signature');
            $pdf->SetAuthor('WP Electronic Signature Plugin');
            $pdf->SetTitle('电子合同');
            $pdf->SetSubject('电子签名合同');

            // 设置页边距
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // 添加页面
            $pdf->AddPage();
            $debug_info[] = '✅ PDF页面设置完成';

            // 设置字体
            $pdf->SetFont('helvetica', '', 12);

            // 将HTML转换为TCPDF可以处理的格式
            $debug_info[] = '开始准备HTML内容';
            $tcpdf_html = $this->prepare_html_for_tcpdf_with_debug($html_content, $debug_info);
            $debug_info[] = '✅ HTML准备完成，处理后长度: ' . strlen($tcpdf_html);

            // 写入HTML内容
            $debug_info[] = '开始写入HTML内容到PDF';
            try {
                $pdf->writeHTML($tcpdf_html, true, false, true, false, '');
                $debug_info[] = '✅ HTML内容写入成功';
            } catch (Exception $e) {
                $debug_info[] = '❌ HTML写入失败: ' . $e->getMessage();
                return false;
            }

            // 输出PDF
            $debug_info[] = '开始输出PDF文件';
            try {
                $pdf->Output($pdf_path, 'F');
                $debug_info[] = '✅ PDF输出完成';
            } catch (Exception $e) {
                $debug_info[] = '❌ PDF输出失败: ' . $e->getMessage();
                return false;
            }

            // 验证文件
            $debug_info[] = '验证生成的PDF文件';
            if (file_exists($pdf_path)) {
                $file_size = filesize($pdf_path);
                $debug_info[] = '✅ PDF文件存在，大小: ' . $file_size . ' 字节';

                if ($file_size > 100) {
                    $debug_info[] = '✅ PDF生成成功';
                    return true;
                } else {
                    $debug_info[] = '❌ PDF文件过小';
                    return false;
                }
            } else {
                $debug_info[] = '❌ PDF文件不存在';
                return false;
            }

        } catch (Exception $e) {
            $debug_info[] = '❌ PDF生成异常: ' . $e->getMessage();
            $debug_info[] = '异常文件: ' . $e->getFile() . ':' . $e->getLine();
            return false;
        } catch (Error $e) {
            $debug_info[] = '❌ PDF生成致命错误: ' . $e->getMessage();
            $debug_info[] = '错误文件: ' . $e->getFile() . ':' . $e->getLine();
            return false;
        }
    }

    /**
     * 为TCPDF准备HTML内容并收集调试信息
     */
    private function prepare_html_for_tcpdf_with_debug($html_content, &$debug_info) {
        $debug_info[] = '开始为TCPDF准备HTML内容';
        $debug_info[] = '原始HTML长度: ' . strlen($html_content);

        // 移除复杂的CSS样式
        $html_content = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html_content);
        $debug_info[] = '移除CSS后长度: ' . strlen($html_content);

        // 移除JavaScript
        $html_content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html_content);
        $debug_info[] = '移除JS后长度: ' . strlen($html_content);

        // 处理base64图片
        $debug_info[] = '开始处理base64图片';

        // 先检查HTML中所有的img标签
        preg_match_all('/<img[^>]*>/i', $html_content, $all_imgs);
        $debug_info[] = '找到的所有img标签数量: ' . count($all_imgs[0]);

        // 检查每个img标签的src属性
        foreach ($all_imgs[0] as $index => $img_tag) {
            if (preg_match('/src="([^"]*)"/i', $img_tag, $src_match)) {
                $src = $src_match[1];
                $debug_info[] = 'img' . ($index + 1) . ' src: ' . substr($src, 0, 100) . (strlen($src) > 100 ? '...' : '');

                if (strpos($src, 'data:image/') === 0 || strpos($src, 'image/') === 0) {
                    $debug_info[] = '✅ 找到base64图片: img' . ($index + 1);
                } else {
                    $debug_info[] = '❌ 非base64图片: img' . ($index + 1);
                }
            }
        }

        $image_count = 0;
        $html_content = preg_replace_callback(
            '/<img[^>]*src="((?:data:)?image\/[^"]+)"[^>]*>/i',
            function($matches) use (&$image_count, &$debug_info) {
                $image_count++;
                $debug_info[] = '处理第' . $image_count . '个base64图片';

                $base64_data = $matches[1];
                $debug_info[] = 'base64数据长度: ' . strlen($base64_data);

                $temp_image_path = $this->save_base64_image_for_tcpdf($base64_data);

                if ($temp_image_path) {
                    $debug_info[] = '✅ base64图片转换成功: ' . basename($temp_image_path);
                    return '<div style="width: 100%; text-align: center; padding: 10px;"><img src="' . $temp_image_path . '" style="width: 250px; height: 100px; border: 1px solid #999;" /></div>';
                } else {
                    $debug_info[] = '❌ base64图片转换失败，使用文本替代';
                    return '<div style="width: 100%; text-align: center; padding: 20px; color: #666; font-size: 12px; border: 1px solid #ccc; background: #f9f9f9;"><strong>[电子签名已确认]</strong></div>';
                }
            },
            $html_content
        );
        $debug_info[] = '共处理了' . $image_count . '个base64图片';

        // 检查处理后的HTML中是否包含买家签名
        if (strpos($html_content, 'wp-esig-buyer-signature-area') !== false) {
            $debug_info[] = '✅ 找到买家签名区域';

            // 检查签名区域内容
            if (preg_match('/<div[^>]*id="wp-esig-buyer-signature-area"[^>]*>(.*?)<\/div>/s', $html_content, $signature_match)) {
                $signature_content = trim(strip_tags($signature_match[1]));
                $debug_info[] = '买家签名区域内容: ' . substr($signature_content, 0, 100);

                if (strpos($signature_match[1], '<img') !== false) {
                    $debug_info[] = '✅ 买家签名区域包含图片';
                } else {
                    $debug_info[] = '❌ 买家签名区域不包含图片';
                }
            }
        } else {
            $debug_info[] = '❌ 未找到买家签名区域';
        }

        // 优化HTML结构和样式，让PDF更接近前端显示
        $html_content = $this->optimize_html_for_pdf($html_content);

        // 确保基本HTML结构
        if (strpos($html_content, '<html') === false) {
            $html_content = '<html><head><meta charset="UTF-8"></head><body>' . $html_content . '</body></html>';
        }

        // 添加专门为PDF优化的样式
        $tcpdf_styles = $this->get_pdf_optimized_styles();
        $html_content = str_replace('<head>', '<head>' . $tcpdf_styles, $html_content);

        $debug_info[] = '✅ HTML内容准备完成，最终长度: ' . strlen($html_content);
        return $html_content;
    }

    /**
     * 优化HTML结构，让PDF更接近前端显示效果
     */
    private function optimize_html_for_pdf($html_content) {
        // 1. 优化表格样式 - 确保签名区域对齐
        $html_content = preg_replace(
            '/<table[^>]*style="[^"]*"[^>]*>/i',
            '<table style="width: 100%; border-collapse: separate; border-spacing: 15px; margin: 20px 0;">',
            $html_content
        );

        // 2. 优化表格单元格样式 - 降低高度
        $html_content = preg_replace(
            '/<td[^>]*style="[^"]*"[^>]*>/i',
            '<td style="width: 48%; border: 1px solid #ddd; padding: 15px; background: #fafafa; vertical-align: top; height: 120px;">',
            $html_content
        );

        // 3. 优化签名区域容器 - 使用TCPDF兼容的样式，但保持div结构
        $html_content = preg_replace(
            '/<div[^>]*id="wp-esig-buyer-signature-area"[^>]*style="[^"]*"[^>]*>/i',
            '<div id="wp-esig-buyer-signature-area" style="border: 2px dashed #999; background: #f9f9f9; padding: 15px; margin: 10px 0; text-align: center; height: 80px;">',
            $html_content
        );

        // 4. 优化标题样式
        $html_content = preg_replace(
            '/<h1[^>]*>/i',
            '<h1 style="text-align: center; font-size: 18pt; font-weight: bold; margin: 20px 0; border-bottom: 2px solid #333; padding-bottom: 8px;">',
            $html_content
        );

        $html_content = preg_replace(
            '/<h3[^>]*>/i',
            '<h3 style="font-size: 14pt; font-weight: bold; margin: 15px 0 10px 0; color: #333;">',
            $html_content
        );

        $html_content = preg_replace(
            '/<h4[^>]*>/i',
            '<h4 style="margin: 0 0 10px 0; font-size: 12pt; font-weight: bold; text-align: center; border-bottom: 1px solid #ccc; padding-bottom: 5px;">',
            $html_content
        );

        // 5. 优化段落样式
        $html_content = preg_replace(
            '/<p[^>]*style="margin: 8px 0;"[^>]*>/i',
            '<p style="margin: 6px 0; line-height: 1.4;">',
            $html_content
        );

        // 6. 优化签名图片样式 - 使用TCPDF兼容的固定尺寸
        $html_content = preg_replace(
            '/<img[^>]*style="[^"]*width: 200px[^"]*"[^>]*>/i',
            '<img style="width: 150px; height: 50px; border: 1px solid #999; margin: 0 auto;">',
            $html_content
        );

        // 7. 统一时间元素样式 - 确保左右一致的间距
        $debug_info[] = '� 开始统一时间样式...';

        // 检查时间元素
        $time_count = preg_match_all('/<p[^>]*>Data:[^<]*<\/p>/i', $html_content, $time_matches);
        $debug_info[] = '找到 ' . $time_count . ' 个时间元素';

        if ($time_count > 0) {
            foreach ($time_matches[0] as $index => $match) {
                $debug_info[] = '时间元素 ' . ($index + 1) . ': ' . $match;
            }
        }

        // 统一所有时间元素的样式 - 设置一致的上边距
        $unified_time_style = 'margin: 10px 0 0 0 !important; padding: 0 !important; font-size: 12px !important; color: #666 !important; text-align: left !important;';

        $time_replacement_count = 0;
        $html_content = preg_replace_callback(
            '/<p([^>]*)>(Data:[^<]*)<\/p>/i',
            function($matches) use ($unified_time_style, &$time_replacement_count, &$debug_info) {
                $time_replacement_count++;
                $content = $matches[2]; // "Data: 2025-07-29 05:35:47"
                $debug_info[] = '统一时间样式 ' . $time_replacement_count . ': ' . $content;

                return '<p style="' . $unified_time_style . '">' . $content . '</p>';
            },
            $html_content
        );

        $debug_info[] = '✅ 完成时间样式统一，共处理了 ' . $time_replacement_count . ' 个时间元素';

        // 8. 确保卖家签名图片样式统一 - 使用TCPDF兼容样式
        $html_content = preg_replace(
            '/<img[^>]*src="https:\/\/[^"]*signature\.png"[^>]*>/i',
            '<img src="https://xn--444-1la.com.br/wp-content/uploads/2025/07/signature.png" alt="Assinatura do Vendedor" style="width: 250px; height: 100px; border: 1px solid #999;">',
            $html_content
        );

        return $html_content;
    }

    /**
     * 获取专门为PDF优化的CSS样式
     */
    private function get_pdf_optimized_styles() {
        return '<style>
            /* 基础样式 */
            body {
                font-family: "DejaVu Sans", Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.5;
                color: #333;
                margin: 0;
                padding: 20px;
            }

            /* 容器样式 */
            .container {
                max-width: 100%;
                margin: 0 auto;
            }

            /* 标题样式 */
            h1 {
                text-align: center;
                font-size: 18pt;
                font-weight: bold;
                margin: 20px 0;
                border-bottom: 2px solid #333;
                padding-bottom: 8px;
            }

            h3 {
                font-size: 14pt;
                font-weight: bold;
                margin: 15px 0 10px 0;
                color: #333;
            }

            h4 {
                margin: 0 0 10px 0;
                font-size: 12pt;
                font-weight: bold;
                text-align: center;
                border-bottom: 1px solid #ccc;
                padding-bottom: 5px;
            }

            /* 段落样式 */
            p {
                margin: 6px 0;
                line-height: 1.4;
            }

            /* 表格样式 */
            table {
                width: 100%;
                border-collapse: separate;
                border-spacing: 15px;
                margin: 20px 0;
            }

            td {
                width: 48%;
                border: 1px solid #ddd;
                padding: 15px;
                background: #fafafa;
                vertical-align: top;
                height: 120px;
            }

            /* 签名区域样式 */
            .signature-area {
                margin-top: 40px;
                page-break-inside: avoid;
            }

            /* 签名框样式 - 使用TCPDF兼容的样式 */
            div[style*="border: 2px dashed #999"],
            #wp-esig-buyer-signature-area,
            .signature-box {
                border: 2px dashed #999 !important;
                background: #f9f9f9 !important;
                padding: 15px !important;
                margin: 10px 0 !important;
                text-align: center !important;
                height: 80px !important;
            }

            /* 图片样式 - 使用固定尺寸确保对齐 */
            img {
                width: 250px;
                height: 100px;
                border: 1px solid #999;
                margin: 0 auto;
            }

            /* 强调文本 */
            strong {
                font-weight: bold;
            }

            /* 背景区域 */
            .info-section {
                background: #f9f9f9;
                padding: 12px;
                margin: 15px 0;
            }
        </style>';
    }

    /**
     * 从合同图片生成PDF
     */
    public function generate_pdf_from_contract_image($contract_image_data, $customer_data) {
        error_log('WP Electronic Signature: 开始从图片生成PDF');

        try {
            // 解码base64图片数据
            $image_data = str_replace('data:image/png;base64,', '', $contract_image_data);
            $image_data = base64_decode($image_data);

            if (!$image_data) {
                error_log('WP Electronic Signature: 图片数据解码失败');
                return false;
            }

            // 创建临时图片文件
            $upload_dir = wp_upload_dir();
            $temp_dir = $upload_dir['basedir'] . '/wp-esig-temp/';
            if (!file_exists($temp_dir)) {
                wp_mkdir_p($temp_dir);
            }

            $temp_image_path = $temp_dir . 'contract_' . time() . '.png';
            $result = file_put_contents($temp_image_path, $image_data);

            if ($result === false) {
                error_log('WP Electronic Signature: 临时图片文件创建失败');
                return false;
            }

            error_log('WP Electronic Signature: 临时图片文件创建成功 - ' . $temp_image_path);

            // 生成PDF
            $pdf_path = $this->create_pdf_from_image($temp_image_path, $customer_data);

            // 清理临时图片
            if (file_exists($temp_image_path)) {
                unlink($temp_image_path);
            }

            if ($pdf_path) {
                $upload_url = wp_upload_dir()['baseurl'];
                $download_url = str_replace($upload_dir['basedir'], $upload_url, $pdf_path);

                return array(
                    'file_path' => $pdf_path,
                    'download_url' => $download_url
                );
            }

            return false;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: PDF生成异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用TCPDF将图片嵌入PDF
     */
    private function create_pdf_from_image($image_path, $customer_data) {
        try {
            // 加载TCPDF
            require_once WP_ESIG_PLUGIN_DIR . 'includes/tcpdf/tcpdf.php';

            // 创建PDF实例
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

            // 设置文档信息
            $pdf->SetCreator('WP Electronic Signature');
            $pdf->SetTitle('电子合同 - ' . $customer_data['customer_name']);
            $pdf->SetSubject('电子签名合同');

            // 设置页边距
            $pdf->SetMargins(10, 10, 10);
            $pdf->SetAutoPageBreak(false); // 禁用自动分页，因为我们使用图片

            // 添加页面
            $pdf->AddPage();

            // 获取图片尺寸
            $image_info = getimagesize($image_path);
            if (!$image_info) {
                error_log('WP Electronic Signature: 无法获取图片信息');
                return false;
            }

            $image_width = $image_info[0];
            $image_height = $image_info[1];

            error_log('WP Electronic Signature: 原始图片尺寸 - ' . $image_width . 'x' . $image_height);

            // 计算PDF中的图片尺寸（A4: 210x297mm，减去边距）
            $pdf_width = 190; // 210 - 20 (左右边距)
            $pdf_height = 277; // 297 - 20 (上下边距)

            // 按比例缩放
            $scale_x = $pdf_width / ($image_width / 3.78); // 像素转mm (1mm ≈ 3.78px)
            $scale_y = $pdf_height / ($image_height / 3.78);
            $scale = min($scale_x, $scale_y);

            $final_width = ($image_width / 3.78) * $scale;
            $final_height = ($image_height / 3.78) * $scale;

            // 居中显示
            $x = (210 - $final_width) / 2;
            $y = (297 - $final_height) / 2;

            error_log('WP Electronic Signature: PDF中图片尺寸 - ' . $final_width . 'x' . $final_height . ' at (' . $x . ',' . $y . ')');

            // 插入图片
            $pdf->Image($image_path, $x, $y, $final_width, $final_height, 'PNG');

            // 生成PDF文件
            $pdf_dir = wp_upload_dir()['basedir'] . '/wp-esig-contracts/';
            if (!file_exists($pdf_dir)) {
                wp_mkdir_p($pdf_dir);
            }

            $pdf_filename = 'contract_image_' . time() . '_' . sanitize_file_name($customer_data['customer_name']) . '.pdf';
            $pdf_path = $pdf_dir . $pdf_filename;

            $pdf->Output($pdf_path, 'F');

            // 验证文件生成
            if (file_exists($pdf_path) && filesize($pdf_path) > 1000) {
                error_log('WP Electronic Signature: 图片PDF生成成功: ' . $pdf_path);
                return $pdf_path;
            }

            error_log('WP Electronic Signature: PDF文件生成失败或文件过小');
            return false;

        } catch (Exception $e) {
            error_log('WP Electronic Signature: 图片PDF生成失败: ' . $e->getMessage());
            return false;
        }
    }


}
