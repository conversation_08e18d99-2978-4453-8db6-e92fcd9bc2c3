<?php
/**
 * 邮件发送类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 邮件发送类
 */
class WP_Esig_Email {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 监听签名完成事件
        add_action('wp_esig_signature_completed', array($this, 'send_contract_email'), 10, 2);
        
        // 配置WordPress邮件设置
        add_action('phpmailer_init', array($this, 'configure_smtp'));
    }
    
    /**
     * 配置SMTP设置
     */
    public function configure_smtp($phpmailer) {
        // 检查是否启用自定义SMTP
        if (get_option('wp_esig_smtp_enabled', 'no') !== 'yes') {
            return;
        }
        
        $smtp_host = get_option('wp_esig_smtp_host', '');
        $smtp_port = get_option('wp_esig_smtp_port', '587');
        $smtp_username = get_option('wp_esig_smtp_username', '');
        $smtp_password = get_option('wp_esig_smtp_password', '');
        $smtp_encryption = get_option('wp_esig_smtp_encryption', 'tls');
        
        if (empty($smtp_host) || empty($smtp_username) || empty($smtp_password)) {
            return;
        }
        
        $phpmailer->isSMTP();
        $phpmailer->Host = $smtp_host;
        $phpmailer->Port = $smtp_port;
        $phpmailer->SMTPAuth = true;
        $phpmailer->Username = $smtp_username;
        $phpmailer->Password = $smtp_password;
        $phpmailer->SMTPSecure = $smtp_encryption;
        $phpmailer->From = $smtp_username;
        $phpmailer->FromName = get_option('wp_esig_smtp_from_name', get_bloginfo('name'));
    }
    
    /**
     * 发送合同邮件
     */
    public function send_contract_email($signature_id, $order) {
        if (!$order) {
            error_log('WP Electronic Signature: 订单对象为空，无法发送邮件');
            return false;
        }
        
        // 生成PDF合同
        $pdf_path = $this->generate_contract_pdf($order, $signature_id);
        
        if (!$pdf_path) {
            error_log('WP Electronic Signature: PDF生成失败，无法发送邮件');
            return false;
        }
        
        // 获取收件人邮箱
        $customer_email = $order->get_billing_email();

        // 优先使用会话中保存的卖家邮箱，如果没有则使用设置中的默认值
        $session_seller_email = '';
        if (class_exists('WC') && WC()->session) {
            $session_seller_email = WC()->session->get('wp_esig_seller_email');
        }
        $seller_email = !empty($session_seller_email) ? $session_seller_email : get_option('wp_esig_seller_email', get_option('admin_email'));

        // 检查发送设置
        $send_to_customer = get_option('wp_esig_send_to_customer', 'yes') === 'yes';
        $send_to_seller = get_option('wp_esig_send_to_seller', 'yes') === 'yes';

        // 邮件主题和内容
        $subject = sprintf(__('合同签署完成 - 订单 %s', 'wp-electronic-signature'), $order->get_order_number());

        $customer_sent = false;
        $seller_sent = false;

        // 发送给客户
        if ($send_to_customer && !empty($customer_email)) {
            $customer_message = $this->get_email_template($order, 'customer');
            $customer_sent = $this->send_email_with_attachment($customer_email, $subject, $customer_message, $pdf_path);
        }

        // 发送给卖家
        if ($send_to_seller && !empty($seller_email)) {
            $seller_message = $this->get_email_template($order, 'seller');
            $seller_sent = $this->send_email_with_attachment($seller_email, $subject, $seller_message, $pdf_path);
        }
        
        // 记录发送结果
        if ($customer_sent || $seller_sent) {
            $this->log_email_sent($signature_id, $customer_email, $seller_email, $customer_sent, $seller_sent);
            return true;
        }
        
        return false;
    }

    /**
     * 立即发送合同邮件（不依赖订单）
     */
    public function send_immediate_contract_email($temp_order_data) {
        error_log('WP Electronic Signature: 开始立即发送邮件 - 数据: ' . print_r($temp_order_data, true));

        // 验证必要数据
        if (empty($temp_order_data['customer_email'])) {
            error_log('WP Electronic Signature: 客户邮箱为空，无法发送邮件');
            return false;
        }

        // 生成临时PDF合同
        error_log('WP Electronic Signature: 开始生成PDF合同');
        $pdf_path = $this->generate_immediate_contract_pdf($temp_order_data);

        if (!$pdf_path) {
            error_log('WP Electronic Signature: PDF生成失败，拒绝发送邮件');
            error_log('WP Electronic Signature: 临时订单数据: ' . print_r($temp_order_data, true));
            return false;
        }

        // 验证PDF文件
        if (!file_exists($pdf_path) || filesize($pdf_path) == 0) {
            error_log('WP Electronic Signature: PDF文件不存在或为空，拒绝发送邮件');
            return false;
        }

        // 只有PDF文件确实存在且不为空才继续
        if (pathinfo($pdf_path, PATHINFO_EXTENSION) !== 'pdf') {
            error_log('WP Electronic Signature: 生成的不是PDF文件，拒绝发送邮件: ' . $pdf_path);
            return false;
        }

        error_log('WP Electronic Signature: PDF生成成功: ' . $pdf_path);
        error_log('WP Electronic Signature: PDF文件大小: ' . (file_exists($pdf_path) ? filesize($pdf_path) . ' 字节' : '文件不存在'));

        // 获取收件人邮箱
        $customer_email = $temp_order_data['customer_email'];
        // 使用后台设置的卖家邮箱，而不是前端填写的
        $seller_email = get_option('wp_esig_seller_email', get_option('admin_email'));

        // 简单的邮件主题
        $subject = 'Contrato Assinado - ' . $temp_order_data['customer_name'];

        // 简单的邮件内容（只是为了有邮件正文，重点是PDF附件）
        $simple_message = 'Segue em anexo o contrato assinado.';

        $customer_sent = false;
        $seller_sent = false;

        // 发送给客户（前端填写的买家邮箱）
        if (!empty($customer_email)) {
            error_log('WP Electronic Signature: 准备发送邮件给客户: ' . $customer_email);
            error_log('WP Electronic Signature: 邮件主题: ' . $subject);
            error_log('WP Electronic Signature: PDF文件路径: ' . $pdf_path);
            error_log('WP Electronic Signature: PDF文件是否存在: ' . (file_exists($pdf_path) ? '是' : '否'));

            $customer_sent = $this->send_pdf_only_email($customer_email, $subject, $simple_message, $pdf_path);
            error_log('WP Electronic Signature: 客户邮件发送结果: ' . ($customer_sent ? '成功' : '失败'));
        }

        // 发送给卖家（后台设置的卖家邮箱）
        if (!empty($seller_email)) {
            error_log('WP Electronic Signature: 准备发送邮件给卖家: ' . $seller_email);
            $seller_sent = $this->send_pdf_only_email($seller_email, $subject, $simple_message, $pdf_path);
            error_log('WP Electronic Signature: 卖家邮件发送结果: ' . ($seller_sent ? '成功' : '失败'));
        }

        // 记录发送结果
        if ($customer_sent || $seller_sent) {
            $this->log_simple_email_sent($temp_order_data, $customer_email, $seller_email, $customer_sent, $seller_sent);

            // 清理临时PDF文件
            if (file_exists($pdf_path)) {
                unlink($pdf_path);
            }

            error_log('WP Electronic Signature: 邮件发送成功');
            return true;
        }

        // 清理临时PDF文件
        if (file_exists($pdf_path)) {
            unlink($pdf_path);
        }

        error_log('WP Electronic Signature: 所有邮件发送失败');
        return false;
    }

    /**
     * 发送已生成PDF的合同邮件（用于Confirmar e Continuar按钮）
     */
    public function send_immediate_contract_email_with_pdf($temp_order_data, $pdf_file_path) {
        error_log('WP Electronic Signature: 开始发送已生成PDF的邮件 - PDF路径: ' . $pdf_file_path);
        error_log('WP Electronic Signature: 客户数据: ' . print_r($temp_order_data, true));

        // 验证PDF文件是否存在
        if (!file_exists($pdf_file_path)) {
            error_log('WP Electronic Signature: PDF文件不存在: ' . $pdf_file_path);
            return false;
        }

        // 验证是否为PDF文件
        if (pathinfo($pdf_file_path, PATHINFO_EXTENSION) !== 'pdf') {
            error_log('WP Electronic Signature: 不是PDF文件: ' . $pdf_file_path);
            return false;
        }

        error_log('WP Electronic Signature: PDF文件验证通过，大小: ' . filesize($pdf_file_path) . ' 字节');

        // 获取收件人邮箱
        $customer_email = $temp_order_data['customer_email']; // 用户填写的邮箱
        $seller_email = get_option('wp_esig_seller_email', get_option('admin_email')); // 后台设置的卖家邮箱

        // 邮件主题
        $subject = 'Contrato Assinado - ' . $temp_order_data['customer_name'];

        // 简单的邮件内容
        $simple_message = 'Segue em anexo o contrato assinado.';

        $customer_sent = false;
        $seller_sent = false;

        // 发送给客户（用户填写的邮箱）
        if (!empty($customer_email)) {
            error_log('WP Electronic Signature: 准备发送邮件给客户: ' . $customer_email);
            $customer_sent = $this->send_pdf_only_email($customer_email, $subject, $simple_message, $pdf_file_path);
            error_log('WP Electronic Signature: 客户邮件发送结果: ' . ($customer_sent ? '成功' : '失败'));
        }

        // 发送给卖家（后台设置的邮箱）
        if (!empty($seller_email)) {
            error_log('WP Electronic Signature: 准备发送邮件给卖家: ' . $seller_email);
            $seller_sent = $this->send_pdf_only_email($seller_email, $subject, $simple_message, $pdf_file_path);
            error_log('WP Electronic Signature: 卖家邮件发送结果: ' . ($seller_sent ? '成功' : '失败'));
        }

        // 记录发送结果
        if ($customer_sent || $seller_sent) {
            $this->log_simple_email_sent($temp_order_data, $customer_email, $seller_email, $customer_sent, $seller_sent);
            error_log('WP Electronic Signature: 邮件发送完成，至少一个邮箱发送成功');
            return true;
        }

        error_log('WP Electronic Signature: 所有邮件发送失败');
        return false;
    }

    /**
     * 发送只包含PDF附件的简单邮件
     */
    private function send_pdf_only_email($to, $subject, $message, $pdf_path) {
        error_log('WP Electronic Signature: 准备发送PDF邮件 - 收件人: ' . $to . ', 主题: ' . $subject);
        error_log('WP Electronic Signature: PDF路径: ' . $pdf_path);
        error_log('WP Electronic Signature: PDF是否存在: ' . (file_exists($pdf_path) ? '是' : '否'));

        if (!file_exists($pdf_path)) {
            error_log('WP Electronic Signature: PDF文件不存在，无法发送邮件');
            return false;
        }

        $headers = array('Content-Type: text/html; charset=UTF-8');

        // 直接发送带PDF附件的邮件
        $sent = wp_mail($to, $subject, $message, $headers, array($pdf_path));

        if (!$sent) {
            error_log('WP Electronic Signature: PDF邮件发送失败 - 收件人: ' . $to);

            // 获取WordPress邮件错误信息
            global $phpmailer;
            if (isset($phpmailer) && !empty($phpmailer->ErrorInfo)) {
                error_log('WP Electronic Signature: PHPMailer错误: ' . $phpmailer->ErrorInfo);
            }
        } else {
            error_log('WP Electronic Signature: PDF邮件发送成功 - 收件人: ' . $to);
        }

        return $sent;
    }

    /**
     * 发送带附件的邮件（保留原方法用于其他功能）
     */
    private function send_email_with_attachment($to, $subject, $message, $attachment_path) {
        error_log('WP Electronic Signature: 准备发送邮件 - 收件人: ' . $to . ', 主题: ' . $subject);
        error_log('WP Electronic Signature: 附件路径: ' . $attachment_path);
        error_log('WP Electronic Signature: 附件是否存在: ' . (file_exists($attachment_path) ? '是' : '否'));

        if (!file_exists($attachment_path)) {
            error_log('WP Electronic Signature: 附件文件不存在，无法发送邮件');
            return false;
        }

        $headers = array('Content-Type: text/html; charset=UTF-8');

        // 先尝试发送不带附件的测试邮件
        $test_sent = wp_mail($to, 'Test - ' . $subject, '这是一封测试邮件，用于验证邮件发送功能。', $headers);
        error_log('WP Electronic Signature: 测试邮件发送结果: ' . ($test_sent ? '成功' : '失败'));

        // 发送带附件的邮件
        $sent = wp_mail($to, $subject, $message, $headers, array($attachment_path));

        if (!$sent) {
            error_log('WP Electronic Signature: 邮件发送失败 - 收件人: ' . $to);

            // 获取WordPress邮件错误信息
            global $phpmailer;
            if (isset($phpmailer) && !empty($phpmailer->ErrorInfo)) {
                error_log('WP Electronic Signature: PHPMailer错误: ' . $phpmailer->ErrorInfo);
            }
        } else {
            error_log('WP Electronic Signature: 邮件发送成功 - 收件人: ' . $to);
        }

        return $sent;
    }

    /**
     * 发送不带附件的立即邮件（已禁用）
     */
    private function send_immediate_email_without_attachment($temp_order_data) {
        // 此方法已禁用，不再发送备用邮件
        return false;
        error_log('WP Electronic Signature: 开始发送不带附件的邮件');

        // 获取收件人邮箱
        $customer_email = $temp_order_data['customer_email'];
        // 使用后台设置的卖家邮箱，而不是前端填写的
        $seller_email = get_option('wp_esig_seller_email', get_option('admin_email'));

        // 检查发送设置
        $send_to_customer = get_option('wp_esig_send_to_customer', 'yes') === 'yes';
        $send_to_seller = get_option('wp_esig_send_to_seller', 'yes') === 'yes';

        // 邮件主题和内容
        $subject = sprintf(__('合同签署完成 - %s', 'wp-electronic-signature'), $temp_order_data['customer_name']);

        $customer_sent = false;
        $seller_sent = false;

        // 发送给客户
        if ($send_to_customer && !empty($customer_email)) {
            $customer_message = $this->get_immediate_email_template_without_attachment($temp_order_data, 'customer');
            $customer_sent = $this->send_simple_email($customer_email, $subject, $customer_message);
        }

        // 发送给卖家
        if ($send_to_seller && !empty($seller_email)) {
            $seller_message = $this->get_immediate_email_template_without_attachment($temp_order_data, 'seller');
            $seller_sent = $this->send_simple_email($seller_email, $subject, $seller_message);
        }

        // 记录发送结果
        if ($customer_sent || $seller_sent) {
            $this->log_immediate_email_sent($temp_order_data, $customer_sent, $seller_sent);
            return true;
        }

        return false;
    }

    /**
     * 发送简单邮件（不带附件）
     */
    private function send_simple_email($to, $subject, $message) {
        error_log('WP Electronic Signature: 发送简单邮件到: ' . $to);

        $headers = array('Content-Type: text/html; charset=UTF-8');
        $sent = wp_mail($to, $subject, $message, $headers);

        if (!$sent) {
            error_log('WP Electronic Signature: 简单邮件发送失败 - 收件人: ' . $to);
        } else {
            error_log('WP Electronic Signature: 简单邮件发送成功 - 收件人: ' . $to);
        }

        return $sent;
    }

    /**
     * 获取不带附件的邮件模板
     */
    private function get_immediate_email_template_without_attachment($temp_order_data, $recipient_type = 'customer') {
        if ($recipient_type === 'seller') {
            // 卖家邮件模板
            $template = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">合同签署完成通知 - 卖家</h2>
                <p>您好，</p>
                <p>客户 <strong>%s</strong> 已经完成合同签署。</p>
                <p><strong>客户信息：</strong></p>
                <ul>
                    <li>客户姓名：%s</li>
                    <li>客户邮箱：%s</li>
                    <li>客户电话：%s</li>
                    <li>客户CPF：%s</li>
                    <li>签署时间：%s</li>
                </ul>
                <p><strong>注意：</strong>由于技术原因，PDF合同文件将稍后单独发送。</p>
                <p>请及时处理订单并安排发货。</p>
                <hr>
                <p style="font-size: 12px; color: #666;">此邮件由 %s 自动发送</p>
            </div>';

            return sprintf(
                $template,
                $temp_order_data['customer_name'],
                $temp_order_data['customer_name'],
                $temp_order_data['customer_email'],
                $temp_order_data['customer_phone'],
                $temp_order_data['customer_cpf'],
                current_time('Y-m-d H:i:s'),
                get_bloginfo('name')
            );
        } else {
            // 客户邮件模板
            $template = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">合同签署完成通知</h2>
                <p>尊敬的 %s，</p>
                <p>您的合同已经签署完成。</p>
                <p><strong>签署信息：</strong></p>
                <ul>
                    <li>姓名：%s</li>
                    <li>邮箱：%s</li>
                    <li>CPF：%s</li>
                    <li>签署时间：%s</li>
                </ul>
                <p><strong>注意：</strong>PDF合同文件将稍后单独发送到您的邮箱。</p>
                <p><strong>重要提示：</strong>请检查您的收件箱，如果没有收到邮件，请查看垃圾邮件文件夹。</p>
                <p>如有任何问题，请联系我们。</p>
                <p>谢谢！</p>
                <hr>
                <p style="font-size: 12px; color: #666;">此邮件由 %s 自动发送</p>
            </div>';

            return sprintf(
                $template,
                $temp_order_data['customer_name'],
                $temp_order_data['customer_name'],
                $temp_order_data['customer_email'],
                $temp_order_data['customer_cpf'],
                current_time('Y-m-d H:i:s'),
                get_bloginfo('name')
            );
        }
    }

    /**
     * 生成合同PDF
     */
    private function generate_contract_pdf($order, $signature_id) {
        $pdf_generator = WP_Esig_PDF::get_instance();
        return $pdf_generator->generate_contract_pdf($order, $signature_id);
    }
    

    
    /**
     * 获取邮件模板
     */
    private function get_email_template($order, $recipient_type = 'customer') {
        if ($recipient_type === 'seller') {
            // 卖家邮件模板
            $template = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">合同签署完成通知 - 卖家</h2>
                <p>您好，</p>
                <p>订单 <strong>%s</strong> 的合同已经由客户签署完成。</p>
                <p><strong>订单信息：</strong></p>
                <ul>
                    <li>订单号：%s</li>
                    <li>订单金额：%s</li>
                    <li>客户姓名：%s</li>
                    <li>客户邮箱：%s</li>
                    <li>签署时间：%s</li>
                </ul>
                <p>请查看附件中的已签署合同PDF文件。</p>
                <p>请及时处理订单并安排发货。</p>
                <hr>
                <p style="font-size: 12px; color: #666;">此邮件由 %s 自动发送</p>
            </div>';

            return sprintf(
                $template,
                $order->get_order_number(),
                $order->get_order_number(),
                $order->get_formatted_order_total(),
                $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
                $order->get_billing_email(),
                current_time('Y-m-d H:i:s'),
                get_bloginfo('name')
            );
        } else {
            // 客户邮件模板
            $template = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">合同签署完成通知</h2>
                <p>尊敬的客户，</p>
                <p>您的订单 <strong>%s</strong> 的合同已经签署完成。</p>
                <p><strong>订单信息：</strong></p>
                <ul>
                    <li>订单号：%s</li>
                    <li>订单金额：%s</li>
                    <li>签署时间：%s</li>
                </ul>
                <p>请查看附件中的合同PDF文件。</p>
                <p>如有任何问题，请联系我们。</p>
                <p>谢谢！</p>
                <hr>
                <p style="font-size: 12px; color: #666;">此邮件由 %s 自动发送</p>
            </div>';

            return sprintf(
                $template,
                $order->get_order_number(),
                $order->get_order_number(),
                $order->get_formatted_order_total(),
                current_time('Y-m-d H:i:s'),
                get_bloginfo('name')
            );
        }
    }
    
    /**
     * 获取签名图片路径
     */
    private function get_signature_image_path($filename) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['basedir'] . '/wp-esig-signatures/' . $filename;
    }
    
    /**
     * 记录邮件发送日志
     */
    private function log_email_sent($signature_id, $customer_email, $seller_email, $customer_sent, $seller_sent) {
        $log_message = sprintf(
            'WP Electronic Signature: 邮件发送完成 - 签名ID: %d, 客户邮箱: %s (%s), 卖家邮箱: %s (%s)',
            $signature_id,
            $customer_email,
            $customer_sent ? '成功' : '失败',
            $seller_email,
            $seller_sent ? '成功' : '失败'
        );

        error_log($log_message);
    }

    /**
     * 生成临时合同PDF
     */
    private function generate_immediate_contract_pdf($temp_order_data) {
        $pdf_class = WP_Esig_PDF::get_instance();
        return $pdf_class->generate_immediate_contract_pdf($temp_order_data);
    }

    /**
     * 获取立即发送邮件模板
     */
    private function get_immediate_email_template($temp_order_data, $recipient_type = 'customer') {
        if ($recipient_type === 'seller') {
            // 卖家邮件模板
            $template = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">合同签署完成通知 - 卖家</h2>
                <p>您好，</p>
                <p>客户 <strong>%s</strong> 已经完成合同签署。</p>
                <p><strong>客户信息：</strong></p>
                <ul>
                    <li>客户姓名：%s</li>
                    <li>客户邮箱：%s</li>
                    <li>客户电话：%s</li>
                    <li>客户CPF：%s</li>
                    <li>签署时间：%s</li>
                </ul>
                <p>请查看附件中的已签署合同PDF文件。</p>
                <p>请及时处理订单并安排发货。</p>
                <hr>
                <p style="font-size: 12px; color: #666;">此邮件由 %s 自动发送</p>
            </div>';

            return sprintf(
                $template,
                $temp_order_data['customer_name'],
                $temp_order_data['customer_name'],
                $temp_order_data['customer_email'],
                $temp_order_data['customer_phone'],
                $temp_order_data['customer_cpf'],
                current_time('Y-m-d H:i:s'),
                get_bloginfo('name')
            );
        } else {
            // 客户邮件模板
            $template = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">合同签署完成通知</h2>
                <p>尊敬的 %s，</p>
                <p>您的合同已经签署完成。</p>
                <p><strong>签署信息：</strong></p>
                <ul>
                    <li>姓名：%s</li>
                    <li>邮箱：%s</li>
                    <li>CPF：%s</li>
                    <li>签署时间：%s</li>
                </ul>
                <p>请查看附件中的合同PDF文件。</p>
                <p><strong>重要提示：</strong>请检查您的收件箱，如果没有收到邮件，请查看垃圾邮件文件夹。</p>
                <p>如有任何问题，请联系我们。</p>
                <p>谢谢！</p>
                <hr>
                <p style="font-size: 12px; color: #666;">此邮件由 %s 自动发送</p>
            </div>';

            return sprintf(
                $template,
                $temp_order_data['customer_name'],
                $temp_order_data['customer_name'],
                $temp_order_data['customer_email'],
                $temp_order_data['customer_cpf'],
                current_time('Y-m-d H:i:s'),
                get_bloginfo('name')
            );
        }
    }

    /**
     * 记录简化邮件发送日志
     */
    private function log_simple_email_sent($temp_order_data, $customer_email, $seller_email, $customer_sent, $seller_sent) {
        $log_message = sprintf(
            'WP Electronic Signature: PDF邮件发送完成 - 客户: %s (CPF: %s), 买家邮箱: %s (%s), 卖家邮箱: %s (%s)',
            $temp_order_data['customer_name'],
            $temp_order_data['customer_cpf'],
            $customer_email,
            $customer_sent ? '成功' : '失败',
            $seller_email,
            $seller_sent ? '成功' : '失败'
        );

        error_log($log_message);
    }

    /**
     * 记录立即邮件发送日志（保留原方法）
     */
    private function log_immediate_email_sent($temp_order_data, $customer_sent, $seller_sent) {
        $log_message = sprintf(
            'WP Electronic Signature: 立即邮件发送完成 - 客户: %s (%s), 客户邮箱: %s (%s), 卖家邮箱: %s (%s)',
            $temp_order_data['customer_name'],
            $temp_order_data['customer_cpf'],
            $temp_order_data['customer_email'],
            $customer_sent ? '成功' : '失败',
            isset($temp_order_data['seller_email']) ? $temp_order_data['seller_email'] : 'N/A',
            $seller_sent ? '成功' : '失败'
        );

        error_log($log_message);
    }
    
    /**
     * 测试SMTP连接
     */
    public function test_smtp_connection() {
        $smtp_host = get_option('wp_esig_smtp_host', '');
        $smtp_port = get_option('wp_esig_smtp_port', '587');
        $smtp_username = get_option('wp_esig_smtp_username', '');
        $smtp_password = get_option('wp_esig_smtp_password', '');
        
        if (empty($smtp_host) || empty($smtp_username) || empty($smtp_password)) {
            return array('success' => false, 'message' => '请填写完整的SMTP配置信息');
        }
        
        // 发送测试邮件
        $test_email = get_option('admin_email');
        $subject = 'WP Electronic Signature SMTP测试';
        $message = '这是一封SMTP配置测试邮件，如果您收到此邮件，说明SMTP配置正确。';
        
        $sent = wp_mail($test_email, $subject, $message);
        
        if ($sent) {
            return array('success' => true, 'message' => '测试邮件发送成功！');
        } else {
            return array('success' => false, 'message' => '测试邮件发送失败，请检查SMTP配置。');
        }
    }
}
