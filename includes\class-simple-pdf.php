<?php
/**
 * 简单PDF生成器类
 * 使用纯PHP实现基本的PDF生成功能
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 简单PDF生成器
 */
class WP_Esig_Simple_PDF {
    
    private $content = '';
    private $title = '';
    private $author = '';
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->title = 'Electronic Contract';
        $this->author = 'WP Electronic Signature';
    }
    
    /**
     * 设置PDF标题
     */
    public function setTitle($title) {
        $this->title = $title;
    }
    
    /**
     * 设置PDF作者
     */
    public function setAuthor($author) {
        $this->author = $author;
    }
    
    /**
     * 添加HTML内容
     */
    public function addHTML($html) {
        // 简单的HTML到文本转换
        $text = $this->htmlToText($html);
        $this->content .= $text;
    }
    
    /**
     * 生成PDF文件
     */
    public function output($filename, $mode = 'F') {
        try {
            // 验证内容是否存在
            if (empty($this->content)) {
                error_log('WP Electronic Signature: Simple PDF - 内容为空，无法生成PDF');
                return false;
            }

            // 生成PDF内容
            $pdf_content = $this->generatePDFContent();

            if (empty($pdf_content)) {
                error_log('WP Electronic Signature: Simple PDF - PDF内容生成失败');
                return false;
            }

            if ($mode === 'F') {
                // 验证目录是否存在和可写
                $dir = dirname($filename);
                if (!is_dir($dir)) {
                    error_log('WP Electronic Signature: Simple PDF - 目录不存在: ' . $dir);
                    return false;
                }

                if (!is_writable($dir)) {
                    error_log('WP Electronic Signature: Simple PDF - 目录不可写: ' . $dir);
                    return false;
                }

                // 保存到文件
                $result = file_put_contents($filename, $pdf_content);
                if ($result === false) {
                    error_log('WP Electronic Signature: Simple PDF - 文件写入失败: ' . $filename);
                    return false;
                }

                error_log('WP Electronic Signature: Simple PDF - 文件生成成功: ' . $filename . ', 大小: ' . $result . ' 字节');
                return $result;
            } else {
                // 直接输出
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
                echo $pdf_content;
                return true;
            }
        } catch (Exception $e) {
            error_log('WP Electronic Signature: Simple PDF generation failed: ' . $e->getMessage());
            error_log('WP Electronic Signature: Exception trace: ' . $e->getTraceAsString());
            return false;
        } catch (Error $e) {
            error_log('WP Electronic Signature: Simple PDF fatal error: ' . $e->getMessage());
            error_log('WP Electronic Signature: Error trace: ' . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 生成PDF内容
     */
    private function generatePDFContent() {
        // 基本的PDF结构
        $pdf = "%PDF-1.4\n";
        
        // PDF对象
        $objects = array();
        
        // 对象1: 目录
        $objects[1] = "1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n";
        
        // 对象2: 页面树
        $objects[2] = "2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n";
        
        // 对象3: 页面
        $objects[3] = "3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n/Resources <<\n/Font <<\n/F1 5 0 R\n>>\n>>\n>>\nendobj\n";
        
        // 准备内容
        $content_text = $this->prepareContent();
        $content_length = strlen($content_text);
        
        // 对象4: 页面内容
        $objects[4] = "4 0 obj\n<<\n/Length $content_length\n>>\nstream\n$content_text\nendstream\nendobj\n";
        
        // 对象5: 字体
        $objects[5] = "5 0 obj\n<<\n/Type /Font\n/Subtype /Type1\n/BaseFont /Helvetica\n>>\nendobj\n";
        
        // 对象6: 信息
        $info = "6 0 obj\n<<\n/Title (" . $this->escapeString($this->title) . ")\n/Author (" . $this->escapeString($this->author) . ")\n/Creator (WP Electronic Signature)\n/Producer (Simple PDF Generator)\n/CreationDate (D:" . date('YmdHis') . ")\n>>\nendobj\n";
        $objects[6] = $info;
        
        // 构建PDF
        $offset = strlen($pdf);
        $xref_table = array();
        
        foreach ($objects as $id => $obj) {
            $xref_table[$id] = $offset;
            $pdf .= $obj;
            $offset = strlen($pdf);
        }
        
        // 交叉引用表
        $xref_offset = $offset;
        $pdf .= "xref\n";
        $pdf .= "0 " . (count($objects) + 1) . "\n";
        $pdf .= "0000000000 65535 f \n";
        
        foreach ($xref_table as $id => $pos) {
            $pdf .= sprintf("%010d 00000 n \n", $pos);
        }
        
        // 尾部
        $pdf .= "trailer\n";
        $pdf .= "<<\n";
        $pdf .= "/Size " . (count($objects) + 1) . "\n";
        $pdf .= "/Root 1 0 R\n";
        $pdf .= "/Info 6 0 R\n";
        $pdf .= ">>\n";
        $pdf .= "startxref\n";
        $pdf .= $xref_offset . "\n";
        $pdf .= "%%EOF\n";
        
        return $pdf;
    }
    
    /**
     * 准备页面内容
     */
    private function prepareContent() {
        $content = "BT\n";
        $content .= "/F1 12 Tf\n";
        $content .= "50 750 Td\n";
        
        // 分行处理内容
        $lines = explode("\n", $this->content);
        $y_position = 750;
        
        foreach ($lines as $line) {
            if ($y_position < 50) break; // 防止内容超出页面
            
            $line = $this->escapeString(trim($line));
            if (!empty($line)) {
                $content .= "50 $y_position Td\n";
                $content .= "($line) Tj\n";
                $y_position -= 15;
            }
        }
        
        $content .= "ET\n";
        
        return $content;
    }
    
    /**
     * HTML转文本
     */
    private function htmlToText($html) {
        // 移除HTML标签
        $text = strip_tags($html);
        
        // 解码HTML实体
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        
        // 清理多余的空白
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        // 按行分割，每行最多80个字符
        $lines = array();
        $words = explode(' ', $text);
        $current_line = '';
        
        foreach ($words as $word) {
            if (strlen($current_line . ' ' . $word) <= 80) {
                $current_line .= ($current_line ? ' ' : '') . $word;
            } else {
                if ($current_line) {
                    $lines[] = $current_line;
                }
                $current_line = $word;
            }
        }
        
        if ($current_line) {
            $lines[] = $current_line;
        }
        
        return implode("\n", $lines);
    }
    
    /**
     * 转义PDF字符串
     */
    private function escapeString($string) {
        // 转义特殊字符
        $string = str_replace('\\', '\\\\', $string);
        $string = str_replace('(', '\\(', $string);
        $string = str_replace(')', '\\)', $string);
        $string = str_replace("\r", '\\r', $string);
        $string = str_replace("\n", '\\n', $string);
        $string = str_replace("\t", '\\t', $string);
        
        return $string;
    }
}
