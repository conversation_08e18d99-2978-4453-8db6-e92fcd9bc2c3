/* WP Electronic Signature Frontend Styles */

/* 弹窗模式下隐藏内联签名相关元素 */
.wp-esig-modal .wp-esig-inline-signatures #wp-esig-signature-pad {
    display: none !important;
}

.wp-esig-modal .wp-esig-inline-signatures #wp-esig-signature-controls {
    display: none !important;
}

.wp-esig-modal .wp-esig-inline-signatures #wp-esig-signature-preview {
    display: none !important;
}

.wp-esig-modal .wp-esig-inline-signatures #wp-esig-signature-placeholder {
    display: none !important;
}

/* 签名区域样式 */
.wp-esig-signature-section {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
}

.wp-esig-signature-section h3 {
    margin-top: 0;
    color: #333;
}

/* 合同内容样式 */
.wp-esig-contract-content {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    max-height: 300px;
    overflow-y: auto;
}

.wp-esig-loading {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* CPF输入区域样式 */
.wp-esig-cpf-section {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.wp-esig-cpf-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.wp-esig-cpf-input {
    width: 100%;
    max-width: 200px;
    padding: 10px 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    font-family: monospace;
    letter-spacing: 1px;
    transition: border-color 0.3s ease;
}

.wp-esig-cpf-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.wp-esig-cpf-input.invalid {
    border-color: #dc3232;
}

/* 卖家邮箱输入区域样式 */
.wp-esig-seller-email-section {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.wp-esig-seller-email-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.wp-esig-seller-email-input {
    width: 100%;
    max-width: 300px;
    padding: 10px 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.wp-esig-seller-email-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.wp-esig-seller-email-input.invalid {
    border-color: #dc3232;
}

.wp-esig-field-description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* 买家邮箱输入区域样式 */
.wp-esig-buyer-email-section {
    margin: 20px 0;
    padding: 15px;
    background: #e8f4fd;
    border: 1px solid #b3d9f2;
    border-radius: 5px;
}

.wp-esig-buyer-email-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c5aa0;
    font-size: 14px;
}

.wp-esig-buyer-email-input {
    width: 100%;
    max-width: 350px;
    padding: 10px 12px;
    border: 2px solid #b3d9f2;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background: #ffffff;
}

.wp-esig-buyer-email-input:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.wp-esig-buyer-email-input.invalid {
    border-color: #dc3232;
}

.wp-esig-buyer-email-input::placeholder {
    color: #9ca3af;
    font-style: italic;
}

.wp-esig-error-message {
    color: #dc3232;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 500;
}

/* 签名画板样式 */
.wp-esig-signature-area,
.wp-esig-signature-section {
    margin: 20px 0;
}

.wp-esig-signature-section {
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.wp-esig-signature-section label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

/* 签名画板容器 */
.wp-esig-signature-pad-container {
    position: relative;
    background: #fff;
    margin: 15px 0;
    text-align: center;
    border-radius: 8px;
}

/* 签名占位符（已移除，不再使用） */

.wp-esig-signature-placeholder:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #1976d2;
}

.wp-esig-signature-placeholder p {
    margin: 5px 0;
    color: #007cba;
    font-weight: 500;
    text-align: center;
}

.wp-esig-signature-hint {
    font-size: 12px !important;
    color: #666 !important;
    font-style: italic;
}

/* 签名预览 */
#wp-esig-signature-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    background: #f0f8ff;
    cursor: pointer;
    transition: all 0.3s ease;
}

#wp-esig-signature-preview:hover {
    background: #e3f2fd;
}

#wp-esig-signature-image {
    max-width: 90%;
    max-height: 120px;
    object-fit: contain;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
}

.wp-esig-signature-status {
    margin: 8px 0 0 0 !important;
    color: #28a745 !important;
    font-size: 12px !important;
    font-weight: 600 !important;
}

.wp-esig-signature-area h4 {
    margin-bottom: 10px;
    color: #333;
}

#wp-esig-signature-pad,
#wp-esig-modal-signature-pad {
    border: none;
    border-radius: 0;
    background: transparent;
    cursor: crosshair;
    display: block;
    margin: 0;
    width: 100%;
    height: 150px;
    touch-action: none;
    user-select: none;
}

/* 签名控制按钮 */
.wp-esig-signature-controls {
    text-align: center;
    margin: 10px 0;
}

.wp-esig-signature-controls .button {
    margin: 0 5px;
}

/* 签名预览 */
#wp-esig-signature-preview,
#wp-esig-modal-signature-preview {
    text-align: center;
    margin: 15px 0;
    padding: 15px;
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 5px;
}

#wp-esig-signature-image,
#wp-esig-modal-signature-image {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* 同意条款样式 */
.wp-esig-agreement {
    margin: 20px 0;
    padding: 15px;
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 5px;
}

.wp-esig-agreement label {
    display: flex;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
}

.wp-esig-agreement input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

/* 弹窗样式 */
.wp-esig-modal {
    display: none;
    position: fixed;
    z-index: 999999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.wp-esig-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 5px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.wp-esig-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    position: relative;
}

.wp-esig-modal-header h3 {
    margin: 0;
    color: #333;
}



.wp-esig-modal-body {
    padding: 20px;
}

.wp-esig-modal-actions {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

/* 禁用页面滚动 */
body.wp-esig-modal-open {
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wp-esig-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    #wp-esig-signature-pad,
    #wp-esig-modal-signature-pad {
        width: 100%;
        max-width: 350px;
        height: 150px;
    }
    
    .wp-esig-signature-section {
        padding: 15px;
    }
    
    .wp-esig-modal-header,
    .wp-esig-modal-body {
        padding: 15px;
    }

    .wp-esig-cpf-section,
    .wp-esig-seller-email-section,
    .wp-esig-signature-section,
    .wp-esig-buyer-email-section {
        padding: 10px;
        margin: 15px 0;
    }

    .wp-esig-cpf-input,
    .wp-esig-seller-email-input,
    .wp-esig-buyer-email-input {
        max-width: 100%;
        font-size: 16px; /* 防止iOS缩放 */
    }
}

@media (max-width: 480px) {
    #wp-esig-signature-pad,
    #wp-esig-modal-signature-pad {
        width: 100%;
        max-width: 280px;
        height: 120px;
    }
    
    .wp-esig-signature-controls .button {
        display: block;
        margin: 5px auto;
        width: 100%;
        max-width: 200px;
    }

    .wp-esig-cpf-section label,
    .wp-esig-seller-email-section label,
    .wp-esig-signature-section label,
    .wp-esig-buyer-email-section label {
        font-size: 13px;
    }

    .wp-esig-cpf-input,
    .wp-esig-seller-email-input,
    .wp-esig-buyer-email-input {
        padding: 8px 10px;
        font-size: 14px;
    }

    .wp-esig-signature-pad-container {
        min-height: 120px;
    }

    .wp-esig-signature-placeholder,
    #wp-esig-signature-preview {
        height: 120px;
    }

    #wp-esig-modal-signature-pad {
        height: 120px;
    }

    .wp-esig-signature-placeholder p {
        font-size: 13px;
    }

    .wp-esig-signature-hint {
        font-size: 11px !important;
    }
}

/* 状态样式 */
.wp-esig-success {
    color: #46b450;
}

.wp-esig-error {
    color: #dc3232;
}

.wp-esig-warning {
    color: #ffb900;
}

/* 加载动画 */
.wp-esig-loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #333;
    border-radius: 50%;
    animation: wp-esig-spin 1s linear infinite;
    margin-left: 10px;
    vertical-align: middle;
}

@keyframes wp-esig-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表格布局签名区域样式 - 防止元素覆盖 */
.wp-esig-contract-content table {
    table-layout: fixed !important;
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 20px !important;
}

.wp-esig-contract-content table td {
    width: 50% !important;
    vertical-align: top !important;
    position: relative !important;
    overflow: visible !important;
}

/* 内联签名区域样式 */
#wp-esig-buyer-signature-area {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

#wp-esig-buyer-signature-area:hover {
    border-color: #005a87 !important;
    background-color: #e6f3ff !important;
}

/* 内联模式下的签名占位符禁用点击 */
.wp-esig-inline-signatures #wp-esig-signature-placeholder {
    user-select: none;
    pointer-events: none;
}

/* 确保弹窗中的签名画板能正常显示 */
.wp-esig-modal #wp-esig-modal-signature-pad {
    pointer-events: auto !important;
    touch-action: none !important;
    cursor: crosshair !important;
}

/* 确保弹窗中的签名控制按钮能正常显示 */
.wp-esig-modal .wp-esig-signature-controls {
    pointer-events: auto !important;
    display: block !important;
}

/* 强制确保弹窗中的签名功能正常 */
.wp-esig-modal .wp-esig-signature-pad-container {
    position: relative !important;
    z-index: 100 !important;
    background: #fff !important;
    border: 1px solid #ddd !important;
    border-radius: 5px !important;
    padding: 10px !important;
    margin: 10px 0 !important;
}

.wp-esig-modal #wp-esig-modal-signature-pad {
    display: block !important;
    margin: 0 auto !important;
    border: 1px solid #ccc !important;
    background: transparent !important;
    cursor: crosshair !important;
    pointer-events: auto !important;
    touch-action: none !important;
}

/* 弹窗中的签名画板样式 */
.wp-esig-modal #wp-esig-modal-signature-pad {
    display: block !important;
    border: 2px solid #007cba !important;
    border-radius: 5px !important;
    background: transparent !important;
    cursor: crosshair !important;
    width: 100% !important;
    max-width: 400px !important;
    height: 150px !important;
    margin: 0 auto !important;
    pointer-events: auto !important;
    touch-action: none !important;
}

#wp-esig-signature-pad {
    display: block;
    margin: 0 auto;
    cursor: crosshair;
    pointer-events: auto;
    touch-action: none;
    user-select: none;
}

#wp-esig-signature-controls button {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#wp-esig-signature-controls button:hover {
    opacity: 0.8;
}

#wp-esig-signature-preview {
    text-align: center;
}

#wp-esig-signature-preview img {
    max-width: 100%;
    height: auto;
}

/* 合同签名区域响应式调整 */
@media (max-width: 768px) {
    /* 表格布局响应式调整 - 转换为垂直堆叠 */
    .wp-esig-contract-content table[style*="border-collapse"] {
        display: block !important;
        width: 100% !important;
        border-spacing: 0 !important;
    }

    .wp-esig-contract-content table[style*="border-collapse"] tr {
        display: block !important;
        width: 100% !important;
    }

    .wp-esig-contract-content table[style*="border-collapse"] td {
        display: block !important;
        width: 100% !important;
        margin-bottom: 15px !important;
        border-spacing: 0 !important;
    }

    /* 签名区域内的表格单元格调整 */
    .wp-esig-contract-content td[style*="display: table-cell"] {
        display: block !important;
        height: auto !important;
        min-height: 60px !important;
        padding: 10px !important;
    }

    /* 兼容Flexbox布局 */
    .wp-esig-contract-content div[style*="display: flex"] {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 15px !important;
        min-height: auto !important;
    }

    .wp-esig-contract-content div[style*="flex: 1"] {
        flex: none !important;
        max-width: 100% !important;
        width: 100% !important;
        margin-bottom: 15px !important;
        min-height: auto !important;
    }

    /* 兼容旧的绝对定位布局 */
    .wp-esig-contract-content div[style*="position: relative"] {
        position: static !important;
        min-height: auto !important;
    }

    .wp-esig-contract-content div[style*="position: absolute"] {
        position: static !important;
        width: 100% !important;
        max-width: none !important;
        margin-bottom: 20px !important;
        display: block !important;
    }

    .wp-esig-contract-content div[style*="bottom: 0"] {
        position: static !important;
        bottom: auto !important;
        left: auto !important;
        right: auto !important;
    }

    #wp-esig-buyer-signature-area {
        margin: 10px 0 !important;
    }

    #wp-esig-signature-pad {
        width: 100% !important;
        max-width: 280px !important;
        height: 100px !important;
    }

    /* 确保签名区域文字不被覆盖 */
    .wp-esig-contract-content h3[style*="ASSINATURAS"] {
        margin-bottom: 20px !important;
    }

    .wp-esig-contract-content p[style*="justas e contratadas"] {
        margin-bottom: 30px !important;
    }
}

/* 合同内容样式优化 */
.wp-esig-contract-content {
    font-family: Arial, sans-serif;
    line-height: 1.6;
}

.wp-esig-contract-content h1,
.wp-esig-contract-content h3,
.wp-esig-contract-content h4 {
    color: #333;
    margin-top: 20px;
    margin-bottom: 10px;
}

.wp-esig-contract-content p {
    margin: 8px 0;
}

/* 确保签名区域在移动设备上正确显示 */
@media (max-width: 480px) {
    #wp-esig-signature-controls button {
        display: block;
        width: 100%;
        margin: 5px 0;
        padding: 8px;
        font-size: 12px;
    }
}
