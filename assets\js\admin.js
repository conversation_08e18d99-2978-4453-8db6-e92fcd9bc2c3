/**
 * WP Electronic Signature Admin JavaScript
 */

(function($) {
    'use strict';
    
    var WPEsigAdmin = {
        
        // 初始化
        init: function() {
            this.bindEvents();
            this.initTooltips();
        },
        
        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 设置表单提交确认
            $('#wp-esig-settings-form').on('submit', function(e) {
                if (!self.validateSettings()) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // 签名时机选择变化
            $('select[name="wp_esig_signature_timing"]').on('change', function() {
                self.toggleTimingOptions($(this).val());
            });
            
            // 删除签名记录确认
            $('.wp-esig-delete-signature').on('click', function(e) {
                if (!confirm('确定要删除这条签名记录吗？此操作不可撤销。')) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // 批量操作
            $('#wp-esig-bulk-action').on('click', function() {
                self.handleBulkAction();
            });
        },
        
        // 初始化提示信息
        initTooltips: function() {
            // 为带有data-tooltip属性的元素添加提示
            $('[data-tooltip]').each(function() {
                var $this = $(this);
                var tooltip = $this.attr('data-tooltip');
                
                $this.on('mouseenter', function() {
                    self.showTooltip($this, tooltip);
                }).on('mouseleave', function() {
                    self.hideTooltip();
                });
            });
        },
        
        // 显示提示信息
        showTooltip: function($element, text) {
            var $tooltip = $('<div class="wp-esig-tooltip">' + text + '</div>');
            $('body').append($tooltip);
            
            var offset = $element.offset();
            $tooltip.css({
                position: 'absolute',
                top: offset.top - $tooltip.outerHeight() - 5,
                left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2),
                zIndex: 9999
            });
        },
        
        // 隐藏提示信息
        hideTooltip: function() {
            $('.wp-esig-tooltip').remove();
        },
        
        // 验证设置
        validateSettings: function() {
            var isValid = true;
            var errors = [];
            
            // 验证签名页面标题
            var title = $('input[name="wp_esig_signature_page_title"]').val().trim();
            if (!title) {
                errors.push('签名页面标题不能为空');
                isValid = false;
            }
            
            // 显示错误信息
            if (!isValid) {
                alert('请修正以下错误：\n' + errors.join('\n'));
            }
            
            return isValid;
        },
        
        // 切换签名时机选项
        toggleTimingOptions: function(timing) {
            var $pageLoadOptions = $('.wp-esig-page-load-options');
            var $beforePaymentOptions = $('.wp-esig-before-payment-options');
            
            if (timing === 'on_page_load') {
                $pageLoadOptions.show();
                $beforePaymentOptions.hide();
            } else {
                $pageLoadOptions.hide();
                $beforePaymentOptions.show();
            }
        },
        
        // 处理批量操作
        handleBulkAction: function() {
            var action = $('#bulk-action-selector-top').val();
            var selectedItems = [];
            
            $('input[name="signature_ids[]"]:checked').each(function() {
                selectedItems.push($(this).val());
            });
            
            if (selectedItems.length === 0) {
                alert('请选择要操作的项目');
                return;
            }
            
            if (action === 'delete') {
                if (!confirm('确定要删除选中的 ' + selectedItems.length + ' 条记录吗？此操作不可撤销。')) {
                    return;
                }
                
                this.bulkDeleteSignatures(selectedItems);
            }
        },
        
        // 批量删除签名记录
        bulkDeleteSignatures: function(ids) {
            var self = this;
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_esig_bulk_delete_signatures',
                    signature_ids: ids,
                    nonce: wp_esig_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('删除失败：' + (response.data || '未知错误'));
                    }
                },
                error: function() {
                    alert('删除失败：网络错误');
                }
            });
        },
        
        // 导出签名记录
        exportSignatures: function() {
            var params = new URLSearchParams();
            params.append('action', 'wp_esig_export_signatures');
            params.append('nonce', wp_esig_admin.nonce);
            
            // 添加筛选参数
            var dateFrom = $('#filter-date-from').val();
            var dateTo = $('#filter-date-to').val();
            var status = $('#filter-status').val();
            
            if (dateFrom) params.append('date_from', dateFrom);
            if (dateTo) params.append('date_to', dateTo);
            if (status) params.append('status', status);
            
            // 创建下载链接
            var url = ajaxurl + '?' + params.toString();
            window.open(url, '_blank');
        },
        
        // 刷新统计数据
        refreshStats: function() {
            var self = this;
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_esig_get_stats',
                    nonce: wp_esig_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.updateStatsDisplay(response.data);
                    }
                }
            });
        },
        
        // 更新统计显示
        updateStatsDisplay: function(stats) {
            $('.wp-esig-stat-total .number').text(stats.total || 0);
            $('.wp-esig-stat-today .number').text(stats.today || 0);
            $('.wp-esig-stat-week .number').text(stats.week || 0);
            $('.wp-esig-stat-month .number').text(stats.month || 0);
        },

        // 合同模板相关功能
        initTemplateFeatures: function() {
            var self = this;

            // 确保编辑器正确初始化
            this.ensureEditorInitialization();

            // 模板变量插入功能
            $('.variable-item').on('click', function() {
                var variable = $(this).text().split(' - ')[0];
                self.insertVariableToEditor(variable);
            });

            // 模板预览功能
            $('#template-preview-btn').on('click', function() {
                self.previewTemplate();
            });

            // 模板保存前验证
            $('.wp-esig-template-editor form').on('submit', function(e) {
                if (!self.validateTemplate()) {
                    e.preventDefault();
                    return false;
                }
            });

            // 实时预览功能
            if ($('#template_content').length) {
                this.initRealTimePreview();
            }
        },

        // 确保编辑器正确初始化
        ensureEditorInitialization: function() {
            var self = this;

            // 等待TinyMCE加载完成
            if (typeof tinyMCE !== 'undefined') {
                // 监听编辑器初始化事件
                $(document).on('tinymce-editor-init', function(event, editor) {
                    if (editor.id === 'template_content') {
                        console.log('TinyMCE编辑器已初始化:', editor.id);

                        // 确保编辑器切换功能正常
                        self.setupEditorSwitching(editor);
                    }
                });

                // 如果编辑器已经存在，直接设置
                setTimeout(function() {
                    var editor = tinyMCE.get('template_content');
                    if (editor) {
                        self.setupEditorSwitching(editor);
                    }
                }, 1000);
            }

            // 确保Quicktags正确初始化
            if (typeof QTags !== 'undefined') {
                QTags._buttonsInit();
            }
        },

        // 设置编辑器切换功能
        setupEditorSwitching: function(editor) {
            // 监听编辑器模式切换
            var editorContainer = $('#wp-template_content-wrap');

            // 监听切换按钮点击
            editorContainer.find('.wp-switch-editor').off('click.wp-esig').on('click.wp-esig', function() {
                var mode = $(this).hasClass('switch-tmce') ? 'visual' : 'html';
                console.log('编辑器切换到:', mode);

                // 延迟确保切换完成
                setTimeout(function() {
                    if (mode === 'visual' && !tinyMCE.get('template_content')) {
                        // 如果可视化编辑器没有初始化，重新初始化
                        tinyMCE.execCommand('mceAddEditor', false, 'template_content');
                    }
                }, 100);
            });
        },

        // 插入变量到编辑器
        insertVariableToEditor: function(variable) {
            var editor = tinyMCE.get('template_content');
            var editorContainer = $('#wp-template_content-wrap');

            // 检查当前编辑器模式
            if (editorContainer.hasClass('tmce-active') && editor && !editor.isHidden()) {
                // 可视化模式
                editor.execCommand('mceInsertContent', false, variable);
                editor.focus();
            } else {
                // HTML/代码模式
                var textarea = $('#template_content');
                if (textarea.length) {
                    var cursorPos = textarea.prop('selectionStart');
                    var textBefore = textarea.val().substring(0, cursorPos);
                    var textAfter = textarea.val().substring(cursorPos);
                    textarea.val(textBefore + variable + textAfter);
                    textarea.prop('selectionStart', cursorPos + variable.length);
                    textarea.prop('selectionEnd', cursorPos + variable.length);
                    textarea.focus();
                }
            }
        },

        // 验证模板
        validateTemplate: function() {
            var isValid = true;
            var errors = [];

            // 验证模板名称
            var name = $('input[name="template_name"]').val().trim();
            if (!name) {
                errors.push('模板名称不能为空');
                isValid = false;
            }

            // 验证模板内容 - 改进的内容获取方式
            var content = this.getEditorContent();

            if (!content.trim()) {
                errors.push('模板内容不能为空');
                isValid = false;
            }

            // 检查是否包含签名区域
            if (content.indexOf('party_a') === -1 || content.indexOf('party_b') === -1) {
                if (!confirm('模板中未检测到甲方乙方签名区域，确定要保存吗？')) {
                    isValid = false;
                }
            }

            // 显示错误信息
            if (!isValid && errors.length > 0) {
                alert('请修正以下错误：\n' + errors.join('\n'));
            }

            return isValid;
        },

        // 获取编辑器内容的统一方法
        getEditorContent: function() {
            var editor = tinyMCE.get('template_content');
            var editorContainer = $('#wp-template_content-wrap');

            // 检查当前编辑器模式
            if (editorContainer.hasClass('tmce-active') && editor && !editor.isHidden()) {
                // 可视化模式
                return editor.getContent();
            } else {
                // HTML/代码模式
                return $('#template_content').val();
            }
        },

        // 预览模板
        previewTemplate: function() {
            var content = this.getEditorContent();

            // 替换示例变量
            var previewContent = content
                .replace(/{order_number}/g, 'CONTRATO-001')
                .replace(/{customer_name}/g, '张三')
                .replace(/{customer_email}/g, '<EMAIL>')
                .replace(/{custom_email}/g, '<EMAIL>')
                .replace(/{customer_cpf}/g, '123.456.789-00')
                .replace(/{order_total}/g, '¥299.00')
                .replace(/{order_date}/g, new Date().toLocaleString('zh-CN'))
                .replace(/{order_items}/g, '测试商品 x 1')
                .replace(/{signature_date}/g, new Date().toLocaleString('zh-CN'))
                .replace(/{party_a_name}/g, '测试商店')
                .replace(/{party_a_date}/g, new Date().toLocaleString('zh-CN'))
                .replace(/{party_b_name}/g, '张三')
                .replace(/{party_b_date}/g, new Date().toLocaleString('zh-CN'));

            // 创建预览窗口
            var previewWindow = window.open('', 'template_preview', 'width=800,height=600,scrollbars=yes');
            previewWindow.document.write(`
                <html>
                <head>
                    <title>合同模板预览</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        h2, h3 { color: #333; }
                        .signature-area { margin-top: 50px; position: relative; min-height: 120px; }
                        .party-section { position: absolute; bottom: 0; width: 45%; }
                        .party-a { left: 0; }
                        .party-b { right: 0; }
                    </style>
                </head>
                <body>
                    <h1>合同模板预览</h1>
                    <hr>
                    ${previewContent}
                </body>
                </html>
            `);
            previewWindow.document.close();
        },

        // 初始化实时预览
        initRealTimePreview: function() {
            var self = this;
            var previewContainer = $('#template-live-preview');

            if (previewContainer.length === 0) {
                // 创建预览容器
                previewContainer = $('<div id="template-live-preview" class="wp-esig-template-preview"><h4>实时预览</h4><div class="preview-content"></div></div>');
                $('.wp-esig-template-editor .form-table').after(previewContainer);
            }

            // 绑定内容变化事件
            var updatePreview = function() {
                setTimeout(function() {
                    var content = WPEsigAdmin.getEditorContent();

                    // 简单的变量替换预览
                    var previewContent = content
                        .replace(/{order_number}/g, '<span style="background: #ffffcc;">CONTRATO-001</span>')
                        .replace(/{customer_name}/g, '<span style="background: #ffffcc;">张三</span>')
                        .replace(/{custom_email}/g, '<span style="background: #cceeff;"><EMAIL></span>')
                        .replace(/{customer_cpf}/g, '<span style="background: #ffeecc;">123.456.789-00</span>')
                        .replace(/{party_a_name}/g, '<span style="background: #ccffcc;">测试商店</span>')
                        .replace(/{party_b_name}/g, '<span style="background: #ffcccc;">张三</span>');

                    previewContainer.find('.preview-content').html(previewContent);
                }, 500);
            };

            // 绑定事件
            $('#template_content').on('input', updatePreview);

            // TinyMCE编辑器事件
            if (typeof tinyMCE !== 'undefined') {
                $(document).on('tinymce-editor-init', function(event, editor) {
                    if (editor.id === 'template_content') {
                        editor.on('input change', updatePreview);
                    }
                });
            }

            // 初始预览
            updatePreview();
        }
    };
    
    // 页面加载完成后初始化
    $(document).ready(function() {
        WPEsigAdmin.init();

        // 初始化签名时机选项显示
        var currentTiming = $('select[name="wp_esig_signature_timing"]').val();
        WPEsigAdmin.toggleTimingOptions(currentTiming);

        // 初始化模板功能
        if ($('.wp-esig-template-editor').length || $('.wp-esig-templates-table').length) {
            WPEsigAdmin.initTemplateFeatures();
        }
    });
    
})(jQuery);
